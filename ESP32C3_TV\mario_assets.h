#ifndef MARIO_ASSETS_H
#define MARIO_ASSETS_H

#include <Arduino.h>

// 颜色定义
#define SKY_COLOR      0x5BDF  // 蓝天背景
#define M_RED          0xF801  // 马里奥红色
#define M_SKIN         0xfd28  // 马里奥皮肤色
#define M_SHOES        0xC300  // 马里奥鞋子色
#define M_SHIRT        0x7BCF  // 马里奥衣服色
#define M_HAIR         0x0000  // 马里奥头发色
#define _MASK          SKY_COLOR // 透明掩码颜色（逻辑画框内）
#define _MASK_GOLDEN   0xB420  // 透明掩码颜色（逻辑画框外，复古金色区域）
#define BRICK_MORTAR   0x6180  // 地砖缝隙颜色（深棕色）

// 番茄钟状态颜色定义
#define POMODORO_WORK_COLOR     0xE4E4    // 金黄色（原有颜色）
#define POMODORO_SHORT_BREAK_COLOR  0x07E0    // 绿色
#define POMODORO_LONG_BREAK_COLOR   0x001F    // 蓝色
#define POMODORO_PAUSED_ALPHA   128       // 暂停时的透明度

// 问号方块(19x19像素) - 与原版一致
const uint16_t BLOCK[361] = {
0x5BDF, 0x9A40, 0x9A40, 0x9A40, 0x9A40, 0x9A40, 0x9A40, 0x9A40, 0x9A40, 0x9A40, 0x9A40, 0x9A40, 0x9A40, 0x9A40, 0x9A40, 0x9A40,   // 0x0010 (16) pixels
0x9A40, 0x9A40, 0x5BDF, 0x9A40, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4,   // 0x0020 (32) pixels
0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0x0000, 0x9A40, 0xE4E4, 0x0000, 0x0000, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4,   // 0x0030 (48) pixels
0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0x0000, 0x0000, 0xE4E4, 0x0000, 0x9A40, 0xE4E4, 0x0000, 0x0000, 0xE4E4, 0xE4E4, 0xE4E4,   // 0x0040 (64) pixels
0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0x0000, 0x0000, 0xE4E4, 0x0000, 0x9A40, 0xE4E4, 0xE4E4, 0xE4E4,   // 0x0050 (80) pixels
0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0x0000, 0x9A40,   // 0x0060 (96) pixels
0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4,   // 0x0070 (112) pixels
0xE4E4, 0x0000, 0x9A40, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4,   // 0x0080 (128) pixels
0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0x0000, 0x9A40, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4,   // 0x0090 (144) pixels
0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0x0000, 0x9A40, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4,   // 0x00A0 (160) pixels
0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0x0000, 0x9A40, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4,   // 0x00B0 (176) pixels
0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0x0000, 0x9A40, 0xE4E4,   // 0x00C0 (192) pixels
0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4,   // 0x00D0 (208) pixels
0x0000, 0x9A40, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4,   // 0x00E0 (224) pixels
0xE4E4, 0xE4E4, 0xE4E4, 0x0000, 0x9A40, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4,   // 0x00F0 (240) pixels
0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0x0000, 0x9A40, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4,   // 0x0100 (256) pixels
0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0x0000, 0x9A40, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4,   // 0x0110 (272) pixels
0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0x0000, 0x9A40, 0xE4E4, 0x0000,   // 0x0120 (288) pixels
0x0000, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0x0000, 0x0000, 0xE4E4, 0x0000,   // 0x0130 (304) pixels
0x9A40, 0xE4E4, 0x0000, 0x0000, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0x0000,   // 0x0140 (320) pixels
0x0000, 0xE4E4, 0x0000, 0x9A40, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4,   // 0x0150 (336) pixels
0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0xE4E4, 0x0000, 0x5BDF, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,   // 0x0160 (352) pixels
};

// 问号方块尺寸
const uint8_t BLOCK_SIZE[2] = {19, 19};

// 地面砖块（8x8像素）- 修正为原版棕色缝隙
const uint16_t GROUND_TILE[64] = {
    0xE2C2, 0xF6B6, 0xF6B6, 0xF6B6, BRICK_MORTAR, 0xE2C2, 0xF6B6, 0xE2C2,
    0xF6B6, 0xE2C2, 0xE2C2, 0xE2C2, BRICK_MORTAR, 0xF6B6, 0xE2C2, BRICK_MORTAR,
    0xF6B6, 0xE2C2, 0xE2C2, 0xE2C2, BRICK_MORTAR, 0xE2C2, BRICK_MORTAR, 0xE2C2,
    BRICK_MORTAR, 0xE2C2, 0xE2C2, 0xE2C2, BRICK_MORTAR, 0xF6B6, 0xF6B6, BRICK_MORTAR,
    0xF6B6, BRICK_MORTAR, BRICK_MORTAR, 0xE2C2, BRICK_MORTAR, 0xF6B6, 0xE2C2, BRICK_MORTAR,
    0xF6B6, 0xF6B6, 0xF6B6, BRICK_MORTAR, 0xF6B6, 0xE2C2, 0xE2C2, BRICK_MORTAR,
    0xF6B6, 0xE2C2, 0xE2C2, 0xF6B6, 0xE2C2, 0xE2C2, 0xE2C2, BRICK_MORTAR,
    0xE2C2, BRICK_MORTAR, BRICK_MORTAR, 0xF6B6, BRICK_MORTAR, BRICK_MORTAR, BRICK_MORTAR, 0xE2C2
};

// 云朵精灵 - 与原版一致 (13x12像素)
const uint16_t CLOUD1[156] = {
    SKY_COLOR, 0x0000, 0x0000, 0x0000, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR,
    0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0x0000, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR,
    0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, SKY_COLOR, 0x0000, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR,
    0xFFFF, 0x3DFF, 0xFFFF, 0xFFFF, 0x3DFF, 0xFFFF, 0xFFFF, 0x0000, 0xFFFF, 0x0000, SKY_COLOR, SKY_COLOR, SKY_COLOR,
    0x3DFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, SKY_COLOR, SKY_COLOR,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, SKY_COLOR,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, SKY_COLOR,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, SKY_COLOR, SKY_COLOR,
    0xFFFF, 0xFFFF, 0xFFFF, 0x3DFF, 0x3DFF, 0xFFFF, 0x3DFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, SKY_COLOR,
    0x3DFF, 0x3DFF, 0x3DFF, 0xFFFF, 0xFFFF, 0x3DFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, SKY_COLOR, SKY_COLOR,
    0xFFFF, 0xFFFF, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0x0000, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR,
    0x0000, 0x0000, SKY_COLOR, 0x0000, 0x0000, 0x0000, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR
};

const uint16_t CLOUD2[156] = {
    SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, 0x0000, 0x0000, 0x0000, SKY_COLOR, SKY_COLOR, SKY_COLOR,
    SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0x0000,
    SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000,
    SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, 0x0000, 0xFFFF, 0x3DFF, 0xFFFF, 0xFFFF, 0x3DFF, 0xFFFF, 0xFFFF,
    SKY_COLOR, SKY_COLOR, SKY_COLOR, 0x0000, 0x0000, 0xFFFF, 0x3DFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    SKY_COLOR, SKY_COLOR, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    SKY_COLOR, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x3DFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF,
    SKY_COLOR, SKY_COLOR, 0x0000, 0xFFFF, 0xFFFF, 0x3DFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x3DFF, 0x3DFF, 0xFFFF, 0x3DFF,
    SKY_COLOR, SKY_COLOR, SKY_COLOR, 0x0000, 0xFFFF, 0xFFFF, 0x3DFF, 0x3DFF, 0x3DFF, 0xFFFF, 0xFFFF, 0x3DFF, 0xFFFF,
    SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000,
    SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, 0x0000, 0x0000, SKY_COLOR, 0x0000, 0x0000, 0x0000, SKY_COLOR
};

// 完整的大云朵精灵 - CLOUD2+CLOUD1组合 (26x12像素)
const uint16_t CLOUD3[312] = {
    // 第1行: CLOUD2第1行 + CLOUD1第1行
    SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, 0x0000, 0x0000, 0x0000, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, 0x0000, 0x0000, 0x0000, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR,
    // 第2行: CLOUD2第2行 + CLOUD1第2行
    SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0x0000, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR,
    // 第3行: CLOUD2第3行 + CLOUD1第3行
    SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, SKY_COLOR, 0x0000, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR,
    // 第4行: CLOUD2第4行 + CLOUD1第4行
    SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, 0x0000, 0xFFFF, 0x3DFF, 0xFFFF, 0xFFFF, 0x3DFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x3DFF, 0xFFFF, 0xFFFF, 0x3DFF, 0xFFFF, 0xFFFF, 0x0000, 0xFFFF, 0x0000, SKY_COLOR, SKY_COLOR, SKY_COLOR,
    // 第5行: CLOUD2第5行 + CLOUD1第5行
    SKY_COLOR, SKY_COLOR, SKY_COLOR, 0x0000, 0x0000, 0xFFFF, 0x3DFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x3DFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, SKY_COLOR, SKY_COLOR,
    // 第6行: CLOUD2第6行 + CLOUD1第6行
    SKY_COLOR, SKY_COLOR, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, SKY_COLOR,
    // 第7行: CLOUD2第7行 + CLOUD1第7行
    SKY_COLOR, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, SKY_COLOR,
    // 第8行: CLOUD2第8行 + CLOUD1第8行
    0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x3DFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, SKY_COLOR, SKY_COLOR,
    // 第9行: CLOUD2第9行 + CLOUD1第9行
    SKY_COLOR, SKY_COLOR, 0x0000, 0xFFFF, 0xFFFF, 0x3DFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x3DFF, 0x3DFF, 0xFFFF, 0x3DFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x3DFF, 0x3DFF, 0xFFFF, 0x3DFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, SKY_COLOR,
    // 第10行: CLOUD2第10行 + CLOUD1第10行
    SKY_COLOR, SKY_COLOR, SKY_COLOR, 0x0000, 0xFFFF, 0xFFFF, 0x3DFF, 0x3DFF, 0x3DFF, 0xFFFF, 0xFFFF, 0x3DFF, 0xFFFF, 0x3DFF, 0x3DFF, 0x3DFF, 0xFFFF, 0xFFFF, 0x3DFF, 0xFFFF, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, SKY_COLOR, SKY_COLOR,
    // 第11行: CLOUD2第11行 + CLOUD1第11行
    SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, 0x0000, 0x0000, 0xFFFF, 0xFFFF, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0xFFFF, 0xFFFF, 0x0000, 0xFFFF, 0xFFFF, 0xFFFF, 0x0000, 0x0000, 0x0000, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR,
    // 第12行: CLOUD2第12行 + CLOUD1第12行
    SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, 0x0000, 0x0000, SKY_COLOR, 0x0000, 0x0000, 0x0000, SKY_COLOR, 0x0000, 0x0000, SKY_COLOR, 0x0000, 0x0000, 0x0000, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR, SKY_COLOR
};

// 方形精灵（城墙楼梯） - (20x20像素)
const uint16_t SQUARE [] PROGMEM = {
	0xca61, 0xeccf, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6,
	0xfdf6, 0xfdf6, 0xb40f, 0x0000, 0xeccf, 0xdbca, 0xecae, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6,
	0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xa3ce, 0x3144, 0x0000, 0xfdf6, 0xecae, 0xe42b, 0xecae, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6,
	0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xa3ce, 0x3965, 0x0000, 0x0000, 0xfdf6, 0xfdf6, 0xecae, 0xdbca,
	0xecf0, 0xfdd5, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xf5b5, 0xbc50, 0x3144, 0x0000, 0x0000, 0x0000,
	0xfdf6, 0xfdf6, 0xfdf6, 0xecf0, 0xd305, 0xf552, 0xf593, 0xf593, 0xf593, 0xf593, 0xf593, 0xf593, 0xf593, 0xf593, 0xdcf1, 0x1882,
	0x0000, 0x0000, 0x0000, 0x0000, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdd5, 0xf552, 0xd305, 0xcaa3, 0xcaa3, 0xcaa3, 0xcaa3, 0xcaa3, 0xcaa3,
	0xcaa3, 0xcaa3, 0xba63, 0x1040, 0x0000, 0x0000, 0x0000, 0x0000, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xf593, 0xcaa3, 0xca61, 0xca61,
	0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xb221, 0x1040, 0x0000, 0x0000, 0x0000, 0x0000, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6,
	0xf593, 0xcaa3, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xb221, 0x1040, 0x0000, 0x0000, 0x0000, 0x0000,
	0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xf593, 0xcaa3, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xb221, 0x1040,
	0x0000, 0x0000, 0x0000, 0x0000, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xf593, 0xcaa3, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61,
	0xca61, 0xca61, 0xb221, 0x1040, 0x0000, 0x0000, 0x0000, 0x0000, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xf593, 0xcaa3, 0xca61, 0xca61,
	0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xb221, 0x1040, 0x0000, 0x0000, 0x0000, 0x0000, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6,
	0xf593, 0xcaa3, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xb221, 0x1040, 0x0000, 0x0000, 0x0000, 0x0000,
	0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xf593, 0xcaa3, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xb221, 0x1040,
	0x0000, 0x0000, 0x0000, 0x0000, 0xfdf6, 0xfdf6, 0xfdf6, 0xfdf6, 0xf593, 0xcaa3, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61,
	0xca61, 0xca61, 0xb221, 0x1040, 0x0000, 0x0000, 0x0000, 0x0000, 0xfdf6, 0xfdf6, 0xfdf6, 0xf5b5, 0xdcf1, 0xba63, 0xb221, 0xb221,
	0xb221, 0xb221, 0xb221, 0xb221, 0xb221, 0xb221, 0xa1e1, 0x2060, 0x0000, 0x0000, 0x0000, 0x0000, 0xfdf6, 0xfdf6, 0xfdf6, 0xbc50,
	0x1882, 0x1040, 0x1040, 0x1040, 0x1040, 0x1040, 0x1040, 0x1040, 0x1040, 0x1040, 0x2060, 0xa1e1, 0x30a0, 0x0000, 0x0000, 0x0000,
	0xfdf6, 0xfdf6, 0xa3ce, 0x3144, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x30a0,
	0x7160, 0x40c0, 0x0000, 0x0000, 0xfdf6, 0xa3ce, 0x3965, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x40c0, 0x6120, 0x40c0, 0x0000, 0xb40f, 0x3144, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x40c0, 0x7160, 0x38a0, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x38a0, 0xca61
};

// 方形精灵尺寸（20x20像素，不需要缩放）
const uint8_t SQUARE_SIZE[2] = {20, 20};

// 云朵立方体精灵 - (16x16像素)
const uint16_t CUBE_CLOUD [] PROGMEM = {
	0x949f, 0x949f, 0x949f, 0xe4e4, 0xe4e4, 0xe4e4, 0x949f, 0x949f, 0x949f, 0x949f, 0xe4e4, 0xe4e4, 0xe4e4, 0x949f, 0x949f, 0x949f,
	0x949f, 0x949f, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0x0c80, 0x0c80, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0x949f, 0x949f,
	0x949f, 0x949f, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xffff, 0xffff, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0x949f, 0x949f,
	0x949f, 0x949f, 0x0c80, 0xe4e4, 0xe4e4, 0xe4e4, 0xffff, 0xffff, 0xffff, 0xffff, 0xe4e4, 0xe4e4, 0xe4e4, 0x0c80, 0x949f, 0x949f,
	0x949f, 0x0c80, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0x0c80, 0x949f,
	0x0c80, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0x0c80,
	0x0c80, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0x0c80, 0xffff, 0xffff, 0x0c80, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0x0c80,
	0x0c80, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0x0c80, 0xffff, 0xffff, 0x0c80, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0x0c80,
	0x0c80, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0x0c80, 0xffff, 0xffff, 0x0c80, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0x0c80,
	0x0c80, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0x0c80,
	0x0c80, 0xffff, 0x0c80, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0x0c80, 0xffff, 0x0c80,
	0x949f, 0x0c80, 0xffff, 0xffff, 0xffff, 0x0c80, 0xffff, 0xffff, 0xffff, 0xffff, 0x0c80, 0xffff, 0xffff, 0xffff, 0x0c80, 0x949f,
	0x949f, 0x0c80, 0xffff, 0xffff, 0xffff, 0xffff, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0xffff, 0xffff, 0xffff, 0xffff, 0x0c80, 0x949f,
	0x949f, 0x0c80, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0x0c80, 0x949f,
	0x949f, 0x949f, 0x0c80, 0xffff, 0xffff, 0xffff, 0xffff, 0x0c80, 0x0c80, 0xffff, 0xffff, 0xffff, 0xffff, 0x0c80, 0x949f, 0x949f,
	0x949f, 0x949f, 0x949f, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x949f, 0x949f, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x949f, 0x949f, 0x949f
};

// 云朵立方体精灵尺寸（16x16像素，不需要缩放）
const uint8_t CUBE_CLOUD_SIZE[2] = {16, 16};

// 树藤精灵尺寸（16x16像素，不需要缩放）
const uint8_t VINE_SIZE[2] = {16, 16};

// 栗子精灵尺寸（16x16像素，不需要缩放）
const uint8_t GOOMBA_SIZE[2] = {16, 16};

// 蘑菇精灵尺寸（16x16像素，不需要缩放）
const uint8_t MUSHROOM_SIZE[2] = {16, 16};

// 刺猬精灵尺寸（16x16像素，不需要缩放）
const uint8_t HEDGEHOG_SIZE[2] = {16, 16};

// 树藤精灵 - (16x16像素)
const uint16_t VINE [] PROGMEM = {
	0xB420, 0xB420, 0xB420, 0x0400, 0x0400, 0x0400, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420,
	0xB420, 0xB420, 0x0400, 0x0400, 0x0400, 0x0400, 0x0400, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420,
	0xB420, 0x0400, 0x0400, 0xe4e4, 0xe4e4, 0xe4e4, 0x0400, 0x0400, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420,
	0xB420, 0x0400, 0x0400, 0x0400, 0x0400, 0x0400, 0xe4e4, 0x0400, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420,
	0xB420, 0xB420, 0xB420, 0x0400, 0x0400, 0x0400, 0xB420, 0x0400, 0x0400, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420,
	0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0x0400, 0x0400, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420,
	0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0x0400, 0x0400, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420,
	0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0x0400, 0x0400, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420,
	0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0x0400, 0x0400, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420,
	0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0x0400, 0x0400, 0xB420, 0xB420, 0x0400, 0x0400, 0xB420, 0xB420, 0xB420,
	0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0x0400, 0x0400, 0xB420, 0x0400, 0x0400, 0x0400, 0x0400, 0xB420, 0xB420,
	0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0x0400, 0x0400, 0x0400, 0xe4e4, 0xe4e4, 0xe4e4, 0x0400, 0xB420, 0xB420,
	0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0x0400, 0x0400, 0x0400, 0x0400, 0x0400, 0x0400, 0xe4e4, 0x0400, 0xB420,
	0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0x0400, 0x0400, 0xB420, 0x0400, 0x0400, 0x0400, 0x0400, 0x0400, 0xB420,
	0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0x0400, 0x0400, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420,
	0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0x0400, 0x0400, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420, 0xB420
};

// 管道精灵尺寸（20x20像素，不需要缩放）
const uint8_t PIPE_SIZE[2] = {20, 20};

// 管道精灵 - （20*20像素）
const uint16_t pipeshuiguan20 [] PROGMEM = {
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0540, 0x0540, 0x0540, 0x8682, 0x8682, 0x8682, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 
	0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x0000, 0x0000, 0x8682, 0x0540, 0x0540, 0x8682, 0x8682, 0x8682, 0x0540, 
	0x8682, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x8682, 0x8682, 0x8682, 0x0000, 0x0000, 0x8682, 0x0540, 0x0540, 
	0x8682, 0x8682, 0x8682, 0x0540, 0x8682, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x8682, 0x0000, 
	0x0000, 0x8682, 0x0540, 0x0540, 0x8682, 0x8682, 0x8682, 0x0540, 0x8682, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 
	0x0540, 0x0540, 0x8682, 0x0000, 0x0000, 0x8682, 0x0540, 0x0540, 0x8682, 0x8682, 0x8682, 0x0540, 0x8682, 0x0540, 0x0540, 0x0540, 
	0x0540, 0x0540, 0x0540, 0x0540, 0x8682, 0x8682, 0x8682, 0x0000, 0x0000, 0x8682, 0x0540, 0x0540, 0x8682, 0x8682, 0x8682, 0x0540, 
	0x8682, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x8682, 0x8682, 0x8682, 0x0000, 0x0000, 0x8682, 0x0540, 0x0540, 
	0x8682, 0x8682, 0x8682, 0x0540, 0x8682, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x8682, 0x8682, 0x8682, 0x0000, 
	0x0000, 0x8682, 0x0540, 0x0540, 0x8682, 0x8682, 0x8682, 0x0540, 0x8682, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 
	0x0540, 0x0540, 0x8682, 0x0000, 0x5cbf, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x5cbf, 0x5cbf, 0x0000, 0x8682, 0x8682, 0x0540, 0x8682, 0x8682, 0x8682, 
	0x0540, 0x8682, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x8682, 0x8682, 0x8682, 0x0000, 0x5cbf, 0x5cbf, 0x0000, 0x8682, 0x8682, 
	0x0540, 0x8682, 0x8682, 0x8682, 0x0540, 0x8682, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x8682, 0x8682, 0x8682, 0x0000, 0x5cbf, 
	0x5cbf, 0x0000, 0x8682, 0x8682, 0x0540, 0x8682, 0x8682, 0x8682, 0x0540, 0x8682, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x8682, 
	0x8682, 0x8682, 0x0000, 0x5cbf, 0x5cbf, 0x0000, 0x8682, 0x8682, 0x0540, 0x8682, 0x8682, 0x8682, 0x0540, 0x8682, 0x0540, 0x0540, 
	0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x8682, 0x0000, 0x5cbf, 0x5cbf, 0x0000, 0x8682, 0x8682, 0x0540, 0x8682, 0x8682, 0x8682, 
	0x0540, 0x8682, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x8682, 0x0000, 0x5cbf, 0x5cbf, 0x0000, 0x8682, 0x8682, 
	0x0540, 0x8682, 0x8682, 0x8682, 0x0540, 0x8682, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x8682, 0x8682, 0x8682, 0x0000, 0x5cbf, 
	0x5cbf, 0x0000, 0x8682, 0x8682, 0x0540, 0x8682, 0x8682, 0x8682, 0x0540, 0x8682, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x8682, 
	0x8682, 0x8682, 0x0000, 0x5cbf, 0x5cbf, 0x0000, 0x8682, 0x8682, 0x0540, 0x8682, 0x8682, 0x8682, 0x0540, 0x8682, 0x0540, 0x0540, 
	0x0540, 0x0540, 0x0540, 0x8682, 0x8682, 0x8682, 0x0000, 0x5cbf, 0x5cbf, 0x0000, 0x8682, 0x8682, 0x0540, 0x8682, 0x8682, 0x8682, 
	0x0540, 0x8682, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x8682, 0x0000, 0x5cbf, 0x5cbf, 0x0000, 0x8682, 0x8682, 
	0x0540, 0x8682, 0x8682, 0x8682, 0x0540, 0x8682, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x0540, 0x8682, 0x0000, 0x5cbf
};

// 砖墙精灵 -（8x8像素）
const uint16_t WALL [] PROGMEM = {
	0x9a40, 0x9a40, 0x9a40, 0x0000, 0x9a40, 0x9a40, 0x9a40, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x9a40, 0x71a0, 0x9a40, 0x9a40, 0x9a40, 0x0000, 0x9a40, 0x9a40, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x9a40, 0x9a40, 0x9a40, 0x0000, 0x9a40, 0x9a40, 0x9a40, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x9a40, 0x71a0, 0x9a40, 0x9a40, 0x9a40, 0x0000, 0x9a40, 0x9a40, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000
};

// 山丘精灵 - 使用原版数据 (20x22像素)
const unsigned short HILL[440] ={
0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF,   // 0x0010 (16) pixels
0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF,   // 0x0020 (32) pixels
0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF,   // 0x0030 (48) pixels
0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF,   // 0x0040 (64) pixels
0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF,   // 0x0050 (80) pixels
0x0000, 0x0000, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF,   // 0x0060 (96) pixels
0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x0560, 0x0560, 0x0000, 0x0000, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF,   // 0x0070 (112) pixels
0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x0560, 0x0560, 0x0560, 0x0560, 0x0000, 0x5BDF, 0x5BDF, 0x5BDF,   // 0x0080 (128) pixels
0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x0560, 0x0560, 0x0000, 0x0560,   // 0x0090 (144) pixels
0x0560, 0x0000, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF,   // 0x00A0 (160) pixels
0x0560, 0x0560, 0x0000, 0x0560, 0x0560, 0x0560, 0x0000, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF,   // 0x00B0 (176) pixels
0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x0000, 0x0560, 0x0000, 0x0560, 0x0560, 0x0560, 0x0560, 0x0000, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF,   // 0x00C0 (192) pixels
0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x0000, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560,   // 0x00D0 (208) pixels
0x0000, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x0560, 0x0560, 0x0560, 0x0560,   // 0x00E0 (224) pixels
0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0000, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF,   // 0x00F0 (240) pixels
0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0000, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF,   // 0x0100 (256) pixels
0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0000,   // 0x0110 (272) pixels
0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560,   // 0x0120 (288) pixels
0x0560, 0x0560, 0x0560, 0x0560, 0x0000, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x0560, 0x0560, 0x0560, 0x0560,   // 0x0130 (304) pixels
0x0560, 0x0560, 0x0000, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0000, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF,   // 0x0140 (320) pixels
0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0000, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0000, 0x5BDF,   // 0x0150 (336) pixels
0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x0560, 0x0560, 0x0560, 0x0560, 0x0000, 0x0560, 0x0000, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560,   // 0x0160 (352) pixels
0x0560, 0x0560, 0x0560, 0x0000, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x0560, 0x0560, 0x0560, 0x0560, 0x0000, 0x0560, 0x0560, 0x0560,   // 0x0170 (368) pixels
0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0000, 0x5BDF, 0x5BDF, 0x5BDF, 0x0560, 0x0560, 0x0560, 0x0560,   // 0x0180 (384) pixels
0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0000, 0x5BDF, 0x5BDF,   // 0x0190 (400) pixels
0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560,   // 0x01A0 (416) pixels
0x0560, 0x0560, 0x0000, 0x5BDF, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560,   // 0x01B0 (432) pixels
0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560, 0x0560 };

// 树丛精灵 - 使用原版数据 (21x9像素)
const unsigned short BUSH[189] ={
0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x0000, 0x0000, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF,   // 0x0010 (16) pixels
0x5BDF, 0x0000, 0x0000, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x0000, 0xBFE3, 0xBFE3, 0x0000,   // 0x0020 (32) pixels
0x5BDF, 0x0000, 0x5BDF, 0x5BDF, 0x5BDF, 0x0000, 0xBFE3, 0xBFE3, 0x0000, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF,   // 0x0030 (48) pixels
0x0000, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0x0000, 0xBFE3, 0x0000, 0x5BDF, 0x0000, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0x0000, 0x5BDF,   // 0x0040 (64) pixels
0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x0000, 0xBFE3, 0xBFE3, 0xBFE3, 0x0560, 0xBFE3, 0xBFE3, 0x0000, 0x5BDF, 0x0000, 0xBFE3,   // 0x0050 (80) pixels
0xBFE3, 0xBFE3, 0x0560, 0xBFE3, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x5BDF, 0x0000, 0xBFE3, 0x0560, 0x0560, 0xBFE3, 0xBFE3, 0x0560,   // 0x0060 (96) pixels
0xBFE3, 0xBFE3, 0x0000, 0xBFE3, 0x0560, 0x0560, 0xBFE3, 0xBFE3, 0x0560, 0x5BDF, 0x5BDF, 0x5BDF, 0x0000, 0x0000, 0xBFE3, 0x0560,   // 0x0070 (112) pixels
0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0x0560, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0x5BDF, 0x5BDF,   // 0x0080 (128) pixels
0x0000, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3,   // 0x0090 (144) pixels
0xBFE3, 0xBFE3, 0xBFE3, 0x5BDF, 0x5BDF, 0x0000, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3,   // 0x00A0 (160) pixels
0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0x5BDF, 0x0000, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3,   // 0x00B0 (176) pixels
0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3, 0xBFE3 };

// WIFI精灵（带问号的砖） - (20x20像素)
const uint16_t WIFI_LOGO [] PROGMEM = {
	0xB420, 0xa30a, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61, 0xca61,
	0xca61, 0xca61, 0xa30a, 0xB420, 0xa30a, 0xdbc6, 0xec05, 0xec05, 0xec05, 0xec05, 0xec05, 0xec05, 0xec05, 0xec05, 0xec05, 0xec05,
	0xec05, 0xec05, 0xec05, 0xec05, 0xec05, 0xec05, 0xab46, 0x1969, 0xca61, 0xec05, 0xbb85, 0xa304, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7,
	0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xa304, 0xbb85, 0xb344, 0x0000, 0xca61, 0xec05, 0xa304, 0x8263,
	0xfcc7, 0xfcc7, 0xf445, 0xec05, 0xec05, 0xec05, 0xec05, 0xec05, 0xf466, 0xfcc7, 0xfcc7, 0xfcc7, 0x8263, 0xa304, 0xb344, 0x0000,
	0xca61, 0xec05, 0xfcc7, 0xfcc7, 0xfca6, 0xf486, 0xd303, 0xba41, 0xb221, 0xb221, 0xb221, 0xc241, 0xdb64, 0xfc86, 0xfcc7, 0xfcc7,
	0xfcc7, 0xfcc7, 0xb344, 0x0000, 0xca61, 0xec05, 0xfcc7, 0xfcc7, 0xf486, 0xd2c2, 0xca61, 0x6940, 0x1040, 0x1040, 0x1040, 0x91a1,
	0xca81, 0xdb43, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xb344, 0x0000, 0xca61, 0xec05, 0xfcc7, 0xfcc7, 0xf486, 0xcaa2, 0xca61, 0x6120,
	0x3101, 0xb344, 0xb344, 0xc2a2, 0xca61, 0xa201, 0x59c2, 0xec66, 0xfcc7, 0xfcc7, 0xb344, 0x0000, 0xca61, 0xec05, 0xfcc7, 0xfcc7,
	0xf486, 0xcaa2, 0xca61, 0x6120, 0x4962, 0xfcc7, 0xfcc7, 0xdb03, 0xca61, 0x89a1, 0x1860, 0xe446, 0xfcc7, 0xfcc7, 0xb344, 0x0000,
	0xca61, 0xec05, 0xfcc7, 0xfcc7, 0xfc86, 0xdb43, 0xa201, 0x40c0, 0x4962, 0xfca6, 0xec05, 0xd2e2, 0xca61, 0x89a1, 0x1860, 0xe446,
	0xfcc7, 0xfcc7, 0xb344, 0x0000, 0xca61, 0xec05, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0x59c2, 0x1860, 0x59a2, 0xf446, 0xca82, 0xba21,
	0xb221, 0x7981, 0x1860, 0xe446, 0xfcc7, 0xfcc7, 0xb344, 0x0000, 0xca61, 0xec05, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xec66, 0xe446,
	0xdbc5, 0xca82, 0xca61, 0x48e0, 0x1040, 0x0820, 0x1860, 0xe446, 0xfcc7, 0xfcc7, 0xb344, 0x0000, 0xca61, 0xec05, 0xfcc7, 0xfcc7,
	0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xec05, 0xca61, 0xca61, 0x38a0, 0x59a2, 0xb344, 0xbb65, 0xf486, 0xfcc7, 0xfcc7, 0xb344, 0x0000,
	0xca61, 0xec05, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xf466, 0xd343, 0x7161, 0x1860, 0x7a63, 0xfcc7, 0xfcc7, 0xfcc7,
	0xfcc7, 0xfcc7, 0xb344, 0x0000, 0xca61, 0xec05, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xf486, 0xdba4, 0x4901, 0x4121,
	0xa304, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xb344, 0x0000, 0xca61, 0xec05, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7,
	0xec05, 0xca61, 0xca61, 0xdba4, 0xec86, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xb344, 0x0000, 0xca61, 0xec05, 0xfcc7, 0xfcc7,
	0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xec05, 0xca61, 0xca61, 0x4901, 0x8aa3, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xb344, 0x0000,
	0xca61, 0xec05, 0xa304, 0x8263, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xf486, 0xdba4, 0x4901, 0x1020, 0x7a63, 0xfcc7, 0xfcc7, 0xfcc7,
	0x8263, 0xa304, 0xb344, 0x0000, 0xca61, 0xec05, 0xbb85, 0xa304, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xfcc7, 0xec86, 0x8aa3, 0x7a63,
	0xbb85, 0xfcc7, 0xfcc7, 0xfcc7, 0xa304, 0xbb85, 0xb344, 0x0000, 0x89a1, 0xa2c3, 0xb344, 0xb344, 0xb344, 0xb344, 0xb344, 0xb344,
	0xb344, 0xb344, 0xb344, 0xb344, 0xb344, 0xb344, 0xb344, 0xb344, 0xb344, 0xb344, 0x7a43, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000
};


// 栗子右精灵, 16x16px
const uint16_t liziyou [] PROGMEM = {
	_MASK, _MASK, _MASK, _MASK, _MASK, _MASK, 0x9a40, 0x9a40, 0x9a40, 0x9a40, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK,
	_MASK, _MASK, _MASK, _MASK, _MASK, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, _MASK, _MASK, _MASK, _MASK, _MASK,
	_MASK, _MASK, _MASK, _MASK, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, _MASK, _MASK, _MASK, _MASK,
	_MASK, _MASK, _MASK, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, _MASK, _MASK, _MASK,
	_MASK, _MASK, 0x9a40, 0x0000, 0x0000, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x0000, 0x0000, 0x9a40, _MASK, _MASK,
	_MASK, 0x9a40, 0x9a40, 0x9a40, 0xfe78, 0x0000, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x0000, 0xfe78, 0x9a40, 0x9a40, 0x9a40, _MASK,
	_MASK, 0x9a40, 0x9a40, 0x9a40, 0xfe78, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xfe78, 0x9a40, 0x9a40, 0x9a40, _MASK,
	0x9a40, 0x9a40, 0x9a40, 0x9a40, 0xfe78, 0x0000, 0xfe78, 0x9a40, 0x9a40, 0xfe78, 0x0000, 0xfe78, 0x9a40, 0x9a40, 0x9a40, 0x9a40,
	0x9a40, 0x9a40, 0x9a40, 0x9a40, 0xfe78, 0xfe78, 0xfe78, 0x9a40, 0x9a40, 0xfe78, 0xfe78, 0xfe78, 0x9a40, 0x9a40, 0x9a40, 0x9a40,
	0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40,
	_MASK, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0x9a40, 0x9a40, 0x9a40, 0x9a40, _MASK,
	_MASK, _MASK, _MASK, _MASK, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0xfe78, _MASK, _MASK, _MASK, _MASK,
	_MASK, _MASK, _MASK, _MASK, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0x0000, 0x0000, _MASK, _MASK,
	_MASK, _MASK, _MASK, 0x0000, 0x0000, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, _MASK,
	_MASK, _MASK, _MASK, 0x0000, 0x0000, 0x0000, 0xfe78, 0xfe78, 0xfe78, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, _MASK,
	_MASK, _MASK, _MASK, _MASK, 0x0000, 0x0000, 0x0000, _MASK, _MASK, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, _MASK, _MASK
};

// 栗子左精灵，16x16px
const uint16_t lizizuo [] PROGMEM = {
	_MASK, _MASK, _MASK, _MASK, _MASK, _MASK, 0x9a40, 0x9a40, 0x9a40, 0x9a40, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK,
	_MASK, _MASK, _MASK, _MASK, _MASK, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, _MASK, _MASK, _MASK, _MASK, _MASK,
	_MASK, _MASK, _MASK, _MASK, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, _MASK, _MASK, _MASK, _MASK,
	_MASK, _MASK, _MASK, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, _MASK, _MASK, _MASK,
	_MASK, _MASK, 0x9a40, 0x0000, 0x0000, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x0000, 0x0000, 0x9a40, _MASK, _MASK,
	_MASK, 0x9a40, 0x9a40, 0x9a40, 0xfe78, 0x0000, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x0000, 0xfe78, 0x9a40, 0x9a40, 0x9a40, _MASK,
	_MASK, 0x9a40, 0x9a40, 0x9a40, 0xfe78, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xfe78, 0x9a40, 0x9a40, 0x9a40, _MASK,
	0x9a40, 0x9a40, 0x9a40, 0x9a40, 0xfe78, 0x0000, 0xfe78, 0x9a40, 0x9a40, 0xfe78, 0x0000, 0xfe78, 0x9a40, 0x9a40, 0x9a40, 0x9a40,
	0x9a40, 0x9a40, 0x9a40, 0x9a40, 0xfe78, 0xfe78, 0xfe78, 0x9a40, 0x9a40, 0xfe78, 0xfe78, 0xfe78, 0x9a40, 0x9a40, 0x9a40, 0x9a40,
	0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40,
	_MASK, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0x9a40, 0x9a40, 0x9a40, 0x9a40, _MASK,
	_MASK, _MASK, _MASK, _MASK, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0xfe78, _MASK, _MASK, _MASK, _MASK,
	_MASK, _MASK, 0x0000, 0x0000, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0xfe78, _MASK, _MASK, _MASK, _MASK,
	_MASK, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0xfe78, 0x0000, 0x0000, _MASK, _MASK, _MASK,
	_MASK, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0xfe78, 0xfe78, 0xfe78, 0x0000, 0x0000, 0x0000, _MASK, _MASK, _MASK,
	_MASK, _MASK, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, _MASK, _MASK, 0x0000, 0x0000, 0x0000, _MASK, _MASK, _MASK, _MASK
};

// 蘑菇精灵，16x16px
const uint16_t mogu [] PROGMEM = {
	_MASK, _MASK, _MASK, _MASK, _MASK, _MASK, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, 
	_MASK, _MASK, _MASK, _MASK, _MASK, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xb184, 0xb184, _MASK, _MASK, _MASK, _MASK, _MASK, 
	_MASK, _MASK, _MASK, _MASK, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xb184, 0xb184, 0xb184, 0xb184, _MASK, _MASK, _MASK, _MASK, 
	_MASK, _MASK, _MASK, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xb184, 0xb184, 0xb184, 0xb184, 0xb184, _MASK, _MASK, _MASK, 
	_MASK, _MASK, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xb184, 0xb184, 0xb184, 0xe4e4, 0xe4e4, _MASK, _MASK, 
	_MASK, 0xe4e4, 0xe4e4, 0xb184, 0xb184, 0xb184, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, _MASK, 
	_MASK, 0xe4e4, 0xb184, 0xb184, 0xb184, 0xb184, 0xb184, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, _MASK, 
	0xe4e4, 0xe4e4, 0xb184, 0xb184, 0xb184, 0xb184, 0xb184, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xb184, 0xb184, 0xe4e4, 0xe4e4, 
	0xe4e4, 0xe4e4, 0xb184, 0xb184, 0xb184, 0xb184, 0xb184, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xb184, 0xb184, 0xb184, 0xe4e4, 
	0xe4e4, 0xe4e4, 0xe4e4, 0xb184, 0xb184, 0xb184, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xb184, 0xb184, 0xe4e4, 
	0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 
	_MASK, 0xe4e4, 0xb184, 0xb184, 0xb184, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xb184, 0xb184, 0xb184, 0xe4e4, _MASK, 
	_MASK, _MASK, _MASK, _MASK, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, _MASK, _MASK, _MASK, _MASK, 
	_MASK, _MASK, _MASK, _MASK, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xe4e4, 0xffff, _MASK, _MASK, _MASK, _MASK, 
	_MASK, _MASK, _MASK, _MASK, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xe4e4, 0xffff, _MASK, _MASK, _MASK, _MASK, 
	_MASK, _MASK, _MASK, _MASK, _MASK, 0xffff, 0xffff, 0xffff, 0xffff, 0xe4e4, 0xffff, _MASK, _MASK, _MASK, _MASK, _MASK
};

// 刺猬精灵1，16x16px
const uint16_t ciwei1 [] PROGMEM = {
	_MASK, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, 
	_MASK, _MASK, _MASK, _MASK, _MASK, _MASK, 0x0000, 0x0000, 0x0000, 0x0000, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, 
	_MASK, _MASK, _MASK, _MASK, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, _MASK, _MASK, _MASK, _MASK, 
	_MASK, _MASK, _MASK, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, _MASK, _MASK, _MASK, 
	_MASK, _MASK, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x9a40, 0x9a40, 0x0000, 0x0000, 0x0000, _MASK, _MASK, 
	_MASK, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x9a40, 0xfe78, 0x9a40, 0x0000, 0x0000, _MASK, _MASK, 
	_MASK, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x9a40, 0x9a40, 0x0000, 0x0000, _MASK, _MASK, 
	0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, _MASK, 
	_MASK, 0x0000, 0x0000, 0x9a40, 0x9a40, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, _MASK, 
	_MASK, 0x0000, 0x0000, 0x0000, 0x9a40, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, _MASK, 
	0x0000, 0x0000, 0xfe78, 0x0000, 0x9a40, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, _MASK, 
	0x0000, 0x0000, 0x0000, 0x0000, 0x9a40, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, _MASK, 
	0x0000, 0x0000, 0x0000, 0x0000, 0x9a40, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, _MASK, 
	_MASK, 0x0000, 0x0000, 0xfe78, 0x9a40, 0x9a40, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 
	_MASK, _MASK, 0xfe78, 0xfe78, 0xfe78, 0x9a40, 0x9a40, 0x0000, 0x0000, 0x9a40, 0x9a40, 0x9a40, 0xfe78, 0xfe78, 0xfe78, _MASK, 
	_MASK, 0xfe78, 0xfe78, 0xfe78, 0xfe78, _MASK, 0x9a40, 0x9a40, 0x9a40, 0x9a40, _MASK, _MASK, 0xfe78, 0xfe78, 0xfe78, 0xfe78
};

// 刺猬精灵2，16x16px
const uint16_t ciwei2 [] PROGMEM = {
	_MASK, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, 0x0000, 0x0000, 0x0000, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, 
	_MASK, _MASK, _MASK, _MASK, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, _MASK, _MASK, _MASK, _MASK, 
	_MASK, _MASK, _MASK, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, _MASK, _MASK, _MASK, 
	_MASK, _MASK, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x9a40, 0x9a40, 0x0000, 0x0000, 0x0000, _MASK, _MASK, 
	_MASK, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x9a40, 0xfe78, 0x9a40, 0x0000, 0x0000, _MASK, _MASK, 
	_MASK, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x9a40, 0x9a40, 0x0000, 0x0000, _MASK, _MASK, 
	0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, _MASK, 
	_MASK, 0x0000, 0x0000, 0x9a40, 0x9a40, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, _MASK, 
	_MASK, 0x0000, 0x0000, 0x0000, 0x9a40, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, _MASK, 
	0x0000, 0x0000, 0xfe78, 0x0000, 0x9a40, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, _MASK, 
	0x0000, 0x0000, 0x0000, 0x0000, 0x9a40, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, _MASK, 
	0x0000, 0x0000, 0x0000, 0x0000, 0x9a40, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, _MASK, 
	_MASK, 0x0000, 0x0000, 0xfe78, 0x9a40, 0x9a40, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 0x9a40, 
	_MASK, _MASK, _MASK, 0xfe78, 0xfe78, 0x9a40, 0x9a40, 0x0000, 0x0000, 0x9a40, 0x9a40, 0x9a40, 0xfe78, 0xfe78, _MASK, _MASK, 
	_MASK, _MASK, _MASK, 0xfe78, 0xfe78, 0xfe78, 0x9a40, 0x9a40, 0x9a40, 0x9a40, _MASK, 0xfe78, 0xfe78, 0xfe78, _MASK, _MASK, 
	_MASK, _MASK, _MASK, _MASK, 0xfe78, 0xfe78, _MASK, _MASK, _MASK, _MASK, _MASK, 0xfe78, 0xfe78, _MASK, _MASK, _MASK
};

// BOSS1精灵 - 使用原版数据 (32x32像素)
const uint16_t BOSS1_SPRITE [] PROGMEM = {
	0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0xffff, 0xffff, 0xffff, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0x949f, 0xe4e4, 0xffff, 0xffff, 0xffff, 0x0c80, 0x0c80, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0x949f, 0xe4e4, 0xe4e4, 0xffff, 0xffff, 0x0c80, 0x0c80, 0x0c80, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0x949f, 0x0c80, 0xe4e4, 0xe4e4, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0xffff, 0xffff, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0x949f, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0xffff, 0xffff, 0x0c80, 0x949f, 0x949f, 0xe4e4, 0x949f, 
	0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0xffff, 0xffff, 0x0c80, 0x0c80, 0xe4e4, 0x949f, 0xe4e4, 
	0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0xe4e4, 0x0c80, 0x0c80, 0x0c80, 0xffff, 0xffff, 0xffff, 0xe4e4, 0xe4e4, 0xe4e4, 
	0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0xe4e4, 0xe4e4, 0xe4e4, 0x0c80, 0x0c80, 0x0c80, 0xffff, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 
	0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0xffff, 0x0c80, 0x0c80, 0x0c80, 0xffff, 
	0xffff, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0xe4e4, 0x0c80, 0xffff, 0xe4e4, 0xe4e4, 0x0c80, 0x0c80, 0xe4e4, 0xe4e4, 0xffff, 0xffff, 
	0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x0c80, 0xe4e4, 0xffff, 0xffff, 0x0c80, 0x0c80, 0xffff, 
	0xffff, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0xe4e4, 0xe4e4, 0x0c80, 0x0c80, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0x949f, 0xffff, 0x949f, 
	0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0xffff, 0xffff, 0xffff, 0x0c80, 0xe4e4, 0xe4e4, 0xffff, 0xffff, 0xffff, 0x0c80, 0xffff, 
	0xffff, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0xe4e4, 0xe4e4, 0x0c80, 0x949f, 0x0c80, 0x949f, 0xffff, 0x949f, 0xffff, 0x949f, 
	0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0xe4e4, 0xffff, 0xffff, 0x0c80, 0x0c80, 0xe4e4, 0xe4e4, 0xffff, 0x0c80, 0x0c80, 0xffff, 
	0xffff, 0xffff, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0x949f, 
	0x949f, 0x949f, 0x949f, 0xffff, 0x0c80, 0xe4e4, 0xe4e4, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 
	0xffff, 0xffff, 0xffff, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0x949f, 0x949f, 
	0x949f, 0x949f, 0x949f, 0xffff, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 
	0x0c80, 0xffff, 0xffff, 0xffff, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0x949f, 0x949f, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0xffff, 0xffff, 0xffff, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 
	0x0c80, 0xffff, 0xffff, 0xffff, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0x949f, 0xffff, 0xffff, 0xffff, 0x0c80, 0x0c80, 0x0c80, 0xe4e4, 0xffff, 0xffff, 0x0c80, 0x0c80, 0xffff, 0xffff, 0xffff, 
	0xffff, 0xffff, 0xffff, 0x0c80, 0x0c80, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0x949f, 0xe4e4, 0xffff, 0xffff, 0x0c80, 0x0c80, 0x0c80, 0xe4e4, 0xe4e4, 0x0c80, 0x0c80, 0xffff, 0xffff, 0xe4e4, 0xe4e4, 
	0xe4e4, 0x0c80, 0x0c80, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0x949f, 0xe4e4, 0xe4e4, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0xffff, 0xffff, 0xe4e4, 0xe4e4, 0xe4e4, 
	0x949f, 0xffff, 0x949f, 0x949f, 0xe4e4, 0xe4e4, 0xe4e4, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0xffff, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0xffff, 0x0c80, 0xe4e4, 0xe4e4, 0xffff, 
	0x949f, 0xe4e4, 0xe4e4, 0xe4e4, 0x949f, 0xffff, 0xe4e4, 0xe4e4, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0xffff, 0xe4e4, 0x0c80, 0x0c80, 0x0c80, 0xffff, 0xffff, 0xffff, 0x0c80, 0x0c80, 0x0c80, 0xffff, 0x0c80, 0x0c80, 0x949f, 0x949f, 
	0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0x949f, 0x949f, 0xe4e4, 0xe4e4, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0x949f, 0x0c80, 0x0c80, 0x0c80, 0xe4e4, 0xffff, 0xffff, 0x0c80, 0x0c80, 0xffff, 0xffff, 0x0c80, 0x0c80, 0xffff, 0xe4e4, 
	0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0x949f, 0xffff, 0xe4e4, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0xffff, 0xffff, 0x0c80, 0x0c80, 0xe4e4, 0xe4e4, 0x0c80, 0x0c80, 0x0c80, 0xffff, 0xffff, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 
	0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0x949f, 0x949f, 0xe4e4, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0xffff, 0xffff, 0xffff, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0xffff, 0xffff, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 
	0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0x949f, 0xffff, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0xe4e4, 0xe4e4, 0x0c80, 0xffff, 0xffff, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0xffff, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 
	0x949f, 0xe4e4, 0xe4e4, 0xe4e4, 0xffff, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0x0c80, 0xe4e4, 0x0c80, 0x0c80, 0xe4e4, 0x0c80, 0x0c80, 0x0c80, 0xffff, 0xffff, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 
	0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0xffff, 0xffff, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x949f, 
	0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0xffff, 0xffff, 0xffff, 0xffff, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x949f, 0x949f, 
	0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x0c80, 0x949f, 0x949f, 0x949f, 
	0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0xffff, 0xffff, 0xffff, 0xffff, 0xffff, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0x0c80, 0x0c80, 0xe4e4, 0xe4e4, 0x949f, 0x949f, 0x949f, 
	0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0xffff, 0xffff, 0xe4e4, 0xffff, 0xffff, 0xe4e4, 0xe4e4, 0xffff, 0xffff, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0x949f, 0x949f, 
	0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xffff, 0xffff, 0xffff, 0xe4e4, 0xffff, 0xffff, 0xffff, 0xe4e4, 0xe4e4, 0xffff, 0xffff, 0x949f, 
	0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 
	0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xe4e4, 0xffff, 0xffff, 0xffff, 
	0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f, 0x949f
};

// BOSS1精灵尺寸（32x32像素，不需要缩放）
const uint8_t BOSS1_SIZE[2] = {32, 32};

// 马里奥精灵数据（与原版一致）
// 静止状态的马里奥精灵 (13x16像素)
const uint16_t MARIO_IDLE [] PROGMEM = {
	_MASK, _MASK, _MASK, M_RED, M_RED, M_RED, M_RED, M_RED, M_RED, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, M_RED,
	M_RED, M_RED, M_RED, M_RED, M_RED, M_RED, M_RED, M_RED, M_RED, _MASK, _MASK, _MASK, M_HAIR, M_HAIR, M_HAIR, M_SKIN,
	M_SKIN, M_HAIR, M_SKIN, M_SKIN, _MASK, _MASK, _MASK, _MASK, M_HAIR, M_SKIN, M_HAIR, M_SKIN, M_SKIN, M_SKIN, M_HAIR, M_SKIN,
	M_SKIN, M_SKIN, M_SKIN, _MASK, _MASK, M_HAIR, M_SKIN, M_HAIR, M_HAIR, M_SKIN, M_SKIN, M_SKIN, M_HAIR, M_SKIN, M_SKIN, M_SKIN,
	M_SKIN, _MASK, M_HAIR, M_HAIR, M_SKIN, M_SKIN, M_SKIN, M_SKIN, M_HAIR, M_HAIR, M_HAIR, M_HAIR, M_HAIR, _MASK, _MASK, _MASK,
	_MASK, M_SKIN, M_SKIN, M_SKIN, M_SKIN, M_SKIN, M_SKIN, M_SKIN, M_SKIN, _MASK, _MASK, _MASK, _MASK, M_SHIRT, M_SHIRT, M_RED,
	M_SHIRT, M_SHIRT, M_SHIRT, M_SHIRT, _MASK, _MASK, _MASK, _MASK, _MASK, M_SHIRT, M_SHIRT, M_SHIRT, M_RED, M_SHIRT, M_SHIRT, M_RED,
	M_SHIRT, M_SHIRT, M_SHIRT, M_SHIRT, _MASK, M_SHIRT, M_SHIRT, M_SHIRT, M_SHIRT, M_RED, M_RED, M_RED, M_RED, M_SHIRT, M_SHIRT, M_SHIRT,
	M_SHIRT, M_SHIRT, M_SKIN, M_SKIN, M_SHIRT, M_RED, M_SKIN, M_RED, M_RED, M_SKIN, M_RED, M_SHIRT, M_SKIN, M_SKIN, M_SKIN, M_SKIN,
	M_SKIN, M_SKIN, M_RED, M_RED, M_RED, M_RED, M_RED, M_RED, M_SKIN, M_SKIN, M_SKIN, M_SKIN, M_SKIN, M_SKIN, M_RED, M_RED,
	M_RED, M_RED, M_RED, M_RED, M_RED, M_RED, M_SKIN, M_SKIN, M_SKIN, _MASK, _MASK, M_RED, M_RED, M_RED, M_RED, _MASK,
	M_RED, M_RED, M_RED, M_RED, _MASK, _MASK, _MASK, M_SHOES, M_SHOES, M_SHOES, M_SHOES, _MASK, _MASK, _MASK, M_SHOES, M_SHOES,
	M_SHOES, M_SHOES, _MASK, M_SHOES, M_SHOES, M_SHOES, M_SHOES, M_SHOES, _MASK, _MASK, _MASK, M_SHOES, M_SHOES, M_SHOES, M_SHOES, M_SHOES
};

// 跳跃的马里奥精灵 (17x16像素)
const uint16_t MARIO_JUMP [] PROGMEM = {
	_MASK, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, M_SKIN, M_SKIN, M_SKIN,
	M_SKIN, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, M_RED, M_RED, M_RED, M_RED, M_RED, M_RED, _MASK, M_SKIN, M_SKIN,
	M_SKIN, M_SKIN, _MASK, _MASK, _MASK, _MASK, _MASK, M_RED, M_RED, M_RED, M_RED, M_RED, M_RED, M_RED, M_RED, M_RED,
	M_SKIN, M_SKIN, M_SKIN, _MASK, _MASK, _MASK, _MASK, _MASK, M_HAIR, M_HAIR, M_HAIR, M_SKIN, M_SKIN, M_HAIR, M_SKIN, M_SKIN,
	M_SHIRT, M_SHIRT, M_SHIRT, M_SHIRT, _MASK, _MASK, _MASK, _MASK, M_HAIR, M_SKIN, M_HAIR, M_SKIN, M_SKIN, M_SKIN, M_HAIR, M_SKIN,
	M_SKIN, M_SHIRT, M_SHIRT, M_SHIRT, M_SHIRT, _MASK, _MASK, _MASK, _MASK, M_HAIR, M_SKIN, M_HAIR, M_HAIR, M_SKIN, M_SKIN, M_SKIN,
	M_HAIR, M_SKIN, M_SKIN, M_SKIN, M_SHIRT, M_SHIRT, _MASK, _MASK, _MASK, _MASK, M_HAIR, M_HAIR, M_SKIN, M_SKIN, M_SKIN, M_SKIN,
	M_HAIR, M_HAIR, M_HAIR, M_HAIR, M_SHIRT, M_SHIRT, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, M_SKIN, M_SKIN, M_SKIN,
	M_SKIN, M_SKIN, M_SKIN, M_SKIN, M_SHIRT, M_SHIRT, _MASK, _MASK, _MASK, _MASK, M_SHIRT, M_SHIRT, M_SHIRT, M_SHIRT, M_SHIRT, M_RED,
	M_SHIRT, M_SHIRT, M_SHIRT, M_RED, M_SHIRT, M_SHIRT, _MASK, _MASK, _MASK, _MASK, M_SHIRT, M_SHIRT, M_SHIRT, M_SHIRT, M_SHIRT, M_SHIRT,
	M_SHIRT, M_RED, M_SHIRT, M_SHIRT, M_SHIRT, M_RED, M_RED, _MASK, M_SHOES, M_SHOES, M_SKIN, M_SKIN, M_SHIRT, M_SHIRT, M_SHIRT, M_SHIRT,
	M_SHIRT, M_SHIRT, M_RED, M_RED, M_RED, M_RED, M_RED, M_RED, _MASK, M_SHOES, M_SHOES, M_SKIN, M_SKIN, M_SKIN, M_SKIN, M_RED,
	M_RED, M_SHIRT, M_RED, M_RED, M_SKIN, M_RED, M_RED, M_SKIN, M_RED, M_SHOES, M_SHOES, M_SHOES, _MASK, M_SKIN, M_SKIN, M_SHOES,
	M_RED, M_RED, M_RED, M_RED, M_RED, M_RED, M_RED, M_RED, M_RED, M_RED, M_SHOES, M_SHOES, M_SHOES, _MASK, _MASK, M_SHOES,
	M_SHOES, M_SHOES, M_RED, M_RED, M_RED, M_RED, M_RED, M_RED, M_RED, M_RED, M_RED, M_SHOES, M_SHOES, M_SHOES, _MASK, M_SHOES,
	M_SHOES, M_SHOES, M_RED, M_RED, M_RED, M_RED, M_RED, M_RED, M_RED, M_RED, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK,
	M_SHOES, M_SHOES, _MASK, M_RED, M_RED, M_RED, M_RED, M_RED, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK, _MASK
};

// 定义马里奥尺寸
const byte MARIO_IDLE_SIZE[2]  = {13, 16};
const byte MARIO_JUMP_SIZE[2]  = {17, 16};

#endif // MARIO_ASSETS_H