#ifndef MARIO_THEME_H
#define MARIO_THEME_H

#include <Arduino.h>
#include "mario_assets.h"

// 定义最大值和最小值宏
#ifndef MAX
#define MAX(a, b) ((a) > (b) ? (a) : (b))
#endif

#ifndef MIN
#define MIN(a, b) ((a) < (b) ? (a) : (b))
#endif

// 番茄钟颜色常量定义（简化版本，只用于马里奥时钟）
#define POMODORO_WORK_COLOR 0xE4E4          // 默认方块颜色
#define POMODORO_SHORT_BREAK_COLOR 0x07E0   // 绿色
#define POMODORO_LONG_BREAK_COLOR 0x001F    // 蓝色
#define POMODORO_PAUSED_ALPHA 128           // 暂停时的透明度

// 屏幕尺寸常量
extern const int OFFSET_X;
extern const int OFFSET_Y;
extern const int VIRTUAL_WIDTH;
extern const int VIRTUAL_HEIGHT;

// --- 修复：提前声明马里奥尺寸常量 ---
extern const byte MARIO_IDLE_SIZE[2];
extern const uint8_t MARIO_JUMP_SIZE[2];

// 新增：马里奥位图数据的extern声明
extern const uint16_t MARIO_IDLE[];
extern const uint16_t MARIO_JUMP[];

// 新增：方形精灵相关声明
extern const uint16_t SQUARE[];
extern const uint8_t SQUARE_SIZE[2];

// 新增：树藤精灵相关声明
extern const uint16_t VINE[];
extern const uint8_t VINE_SIZE[2];

// 管道精灵相关声明
extern const uint16_t pipeshuiguan20[];
extern const uint8_t PIPE_SIZE[2];

// 云朵立方体精灵相关声明
extern const uint16_t CUBE_CLOUD[];
extern const uint8_t CUBE_CLOUD_SIZE[2];

// 栗子精灵相关声明
extern const uint16_t liziyou[];
extern const uint16_t lizizuo[];
extern const uint8_t GOOMBA_SIZE[2];

// 蘑菇精灵相关声明
extern const uint16_t mogu[];
extern const uint8_t MUSHROOM_SIZE[2];

// 刺猬精灵相关声明
extern const uint16_t ciwei1[];
extern const uint16_t ciwei2[];
extern const uint8_t HEDGEHOG_SIZE[2];

// WiFi精灵相关声明
extern const uint16_t WIFI_LOGO[];

// BOSS1精灵相关声明
extern const uint16_t BOSS1_SPRITE[];
extern const uint8_t BOSS1_SIZE[2];

// WiFi热点类前向声明
class WiFiHotspot;
// --- END ---

// 外部绘图函数声明 - 修改为新的函数名
extern void drawPixelVirtual(int16_t x, int16_t y, uint16_t color);
extern void fillRectVirtual(int16_t x, int16_t y, int16_t w, int16_t h, uint16_t color);
extern void updatePixelVirtual(int16_t x, int16_t y, uint16_t color);
extern void updateRectVirtual(int16_t x, int16_t y, int16_t w, int16_t h, uint16_t color);
extern bool initFrameBuffer();
extern void renderFrameBuffer();
extern void cleanupMarioFrameBuffer();
extern bool useFrameBuffer;

// 新增：脏区域管理和帧率控制
extern bool frameBufferDirty;
extern void markDirtyRegion(int16_t x, int16_t y, int16_t w, int16_t h);
extern void resetDirtyRegion();

// 新增：直接在帧缓冲区绘制文本的函数
extern void drawTextToFrameBuffer(String text, int16_t x, int16_t y, uint16_t color, int scale);

// 新增：绘制角落方形精灵的函数
extern void drawCornerSquares();

// 新增：绘制云朵立方体精灵的函数
extern void drawCloudCubeSprite(int16_t x, int16_t y);

// 新增：绘制管道精灵的函数
extern void drawPipeSprite(int16_t x, int16_t y);

// 图层优先级定义（参考原版项目的图层管理）
enum LayerPriority {
    LAYER_BACKGROUND = 0,    // 背景层（天空、山脉、地面）
    LAYER_SCENERY = 1,       // 景物层（树丛、云朵）
    LAYER_SPRITES = 2,       // 精灵层（栗子精灵、动画精灵）
    LAYER_BLOCKS = 3,        // 方块层（时间方块）
    LAYER_MARIO = 4,         // 马里奥层（最高优先级）
    LAYER_UI = 5             // UI层（文本、边框）
};

// 新增：透明图层机制函数
extern void drawTransparentBitmap(int16_t x, int16_t y, const uint16_t* bitmap, int16_t w, int16_t h, int scale);
extern void drawTransparentBitmapDirect(int16_t x, int16_t y, const uint16_t* bitmap, int16_t w, int16_t h, uint16_t maskColor);
extern void drawTransparentBitmapWithLayerCheck(int16_t x, int16_t y, const uint16_t* bitmap, int16_t w, int16_t h, int scale, LayerPriority layer);
extern bool isPixelInsideBlocks(int16_t x, int16_t y);





// 帧率控制常量
#define TARGET_FPS 60
#define FRAME_TIME_MS (1000 / TARGET_FPS)

// 颜色定义（额外的颜色）
#define WHITE        0xFFFF
#define BLACK        0x0000
// SKY_COLOR已在mario_assets.h中定义

// 马里奥精灵尺寸
#define MARIO_WIDTH  48  // 原始16×3
#define MARIO_HEIGHT 48  // 原始16×3

// 方块尺寸 - 使用原始BLOCK_SIZE[0]*3和BLOCK_SIZE[1]*3
#define BLOCK_WIDTH  57  // 原始19×3
#define BLOCK_HEIGHT 57  // 原始19×3

// 方块相关常量
#define MOVE_PACE       6  // 原始2×3
#define MAX_MOVE_HEIGHT 12  // 原始4×3

// 场景元素位置
#define GROUND_HEIGHT 24  // 原始8×3
#define MARIO_START_X 69  // 原始23×3
#define MARIO_START_Y 120  // 原始40×3
#define HOUR_BLOCK_X  39  // 原始13×3
#define HOUR_BLOCK_Y  24   // 原始8×3
#define MIN_BLOCK_X   96  // 原始32×3
#define MIN_BLOCK_Y   24   // 原始8×3

// 马里奥跳跃相关常量
#define MARIO_JUMP_HEIGHT 42  // 原始14×3 (修改为更准确的值)

// 方块状态
enum BlockState {
    IDLE,
    HIT
};

// 方向枚举
enum Direction {
    UP,
    DOWN,
    LEFT,
    RIGHT
};

// 马里奥状态
enum MarioState {
    STANDING,
    JUMPING,
    WALKING
};

// 马里奥类
class Mario {
public:
    Mario();
    void init();
    void update();
    void draw();
    void jump();
    // 新增：获取马里奥的AABB包围盒
    int16_t getX() const { return x; }
    int16_t getY() const { return y; }
    int16_t getWidth() const { return currentWidth; }
    int16_t getHeight() const { return currentHeight; }
    // 新增：碰撞检测接口
    bool collidedWith(int16_t bx, int16_t by, int16_t bwidth, int16_t bheight) const;
    // 新增：跳跃中强制下落
    void forceFall();
    // 新增：跳跃状态判断
    bool jumping() const { return isJumping; }

private:
    int16_t x, y;
    int16_t jumpHeight;
    bool isJumping;
    uint8_t frame;
    MarioState state;
    unsigned long lastUpdate;
    int currentWidth;
    int currentHeight;
};

// 方块类
class Block {
public:
    Block(int16_t x, int16_t y);
    void init();
    void update();
    void draw();
    void setText(String text);
    void hit();  // 方块被击中
    void idle(); // 方块恢复静止
    // 新增：获取AABB包围盒
    int16_t getX() const { return x; }
    int16_t getY() const { return y; }
    int16_t getWidth() const { return BLOCK_WIDTH; }
    int16_t getHeight() const { return BLOCK_HEIGHT; }
    // 新增：碰撞检测接口
    bool collidedWith(const Mario& mario) const;
    // 新增：获取当前状态
    BlockState getState() const;

    // 新增：获取当前颜色（用于测试）
    uint16_t getCurrentColor();

private:
    int16_t x, y;
    int16_t firstY;  // 初始Y位置
    int16_t lastY;   // 上一个Y位置
    String text;
    BlockState state;
    BlockState lastState;
    Direction direction;
    unsigned long lastUpdate;
    void setTextBlock(); // 设置方块上的文字

    // 番茄钟相关方法
    bool isPomodoroMode();
    void displayPomodoroTime();
    void displayCurrentTime();

    // 颜色控制方法
    uint16_t getBlinkingColor();
    void drawBlockWithColor(uint16_t blockColor);
};

// 场景类
class Scene {
public:
    Scene();
    void init();
    void update(); // 新增：场景更新方法，用于云朵动画
    void draw();
    void drawGround();
    void drawClouds();
    void drawHills();
    void drawBushes(); // 添加绘制树丛的方法

    // 新增：重绘指定区域内的云朵部分
    void redrawCloudsInRegion(int16_t x, int16_t y, int16_t w, int16_t h);

    // 新增：性能优化方法
    void pauseCloudAnimation();   // 暂停云朵动画
    void resumeCloudAnimation();  // 恢复云朵动画
    bool isCloudAnimationPaused() const; // 检查动画是否暂停

    // 绘制地面瓦片
    void drawGroundTile(int16_t x, int16_t y);

private:
    unsigned long lastUpdate;
    // For CLOUD3 animation (the main one at y=63)
    int16_t cloud3X;          // Current X of CLOUD3
    int16_t cloud3TargetX;    // Target X of CLOUD3
    bool cloudAnimationActive; // For CLOUD3 animation

    // 新增：云朵循环动画状态
    enum CloudAnimationState {
        MOVING_RIGHT,    // 向右移动阶段
        WAITING_RIGHT,   // 在右侧等待阶段
        MOVING_LEFT,     // 向左移动阶段
        WAITING_LEFT     // 在左侧等待阶段
    };
    CloudAnimationState cloudState;
    unsigned long waitStartTime;  // 等待开始时间
    bool cloudAnimationPaused;    // 动画暂停状态

    // 云朵动画参数
    static const int16_t CLOUD3_LEFT_POS = -39;    // 左侧位置（初始位置）
    static const int16_t CLOUD3_RIGHT_POS = 153;   // 右侧位置（原cloud2的初始位置）
    static const unsigned long WAIT_DURATION = 3000; // 等待时间：3秒

    // For CLOUD3 upper animation (at y=21) - 云朵连接动画
    int16_t cloud3UpperX;          // Current X of upper CLOUD3
    int16_t cloud3UpperTargetX;    // Target X of upper CLOUD3
    CloudAnimationState cloud3UpperState;  // 上方CLOUD3的动画状态
    unsigned long cloud3UpperWaitStartTime; // 上方CLOUD3等待开始时间

    // 上方CLOUD3动画参数（完整循环动画）
    static const int16_t CLOUD3_UPPER_RIGHT_POS = 153;   // 右侧位置（原cloud2初始位置）
    static const int16_t CLOUD3_UPPER_LEFT_POS = -39;    // 左侧位置（与下方cloud3一样的最左位置）
};

// 栗子精灵类
class Goomba {
public:
    Goomba(int16_t x, int16_t y);
    void init();
    void update();  // 新增：更新动画状态
    void draw();

    // 获取位置和尺寸
    int16_t getX() const { return x; }
    int16_t getY() const { return y; }
    int16_t getWidth() const { return GOOMBA_SIZE[0] * 3; }  // 3倍缩放
    int16_t getHeight() const { return GOOMBA_SIZE[1] * 3; } // 3倍缩放

private:
    // 精灵状态枚举
    enum SpriteState {
        LEFT_SPRITE,
        RIGHT_SPRITE
    };

    // 移动状态枚举（与原版项目相同）
    enum MovementState {
        MOVING_RIGHT,
        MOVING_LEFT,
        IDLE_AT_START,
        IDLE_AT_END
    };

    int16_t x, y;
    int16_t startX;    // 起始X位置
    int16_t targetX;   // 目标X位置
    Direction direction;

    SpriteState spriteState;
    MovementState movementState;
    const uint16_t* currentSprite;

    unsigned long lastSpriteChange;
    unsigned long lastMovementUpdate;
    unsigned long idleStartTime;

    // 动画和移动参数（与原版项目相同）
    static const unsigned long SPRITE_ANIMATION_INTERVAL = 400; // 400ms切换一次精灵
    static const unsigned long MOVEMENT_UPDATE_INTERVAL = 200;  // 200ms更新一次位置
    static const unsigned long IDLE_DURATION = 1000;           // 1秒停留时间
    static const int MOVEMENT_DISTANCE = 60;                   // 移动距离60像素（原版20×3）
    static const int MOVEMENT_PACE = 3;                        // 每次移动3像素（原版1×3）

    void drawScaledSprite(int16_t x, int16_t y, const uint16_t* sprite);
    void redrawBackground(int16_t x, int16_t y, int16_t w, int16_t h);
};

// 动画精灵类（蘑菇和刺猬的交替出现，参考原版项目的AnimatedSprite）
class AnimatedSprite {
public:
    AnimatedSprite(int16_t x, int16_t y);
    void init();
    void update();
    void draw();

    // 获取位置和尺寸
    int16_t getX() const { return x; }
    int16_t getY() const { return y; }
    int16_t getWidth() const { return 16 * 3; }   // 3倍缩放
    int16_t getHeight() const { return 16 * 3; }  // 3倍缩放

private:
    // 精灵类型枚举（与原版项目相同）
    enum SpriteType {
        MUSHROOM_SPRITE,
        HEDGEHOG_SPRITE_1,
        HEDGEHOG_SPRITE_2
    };

    // 移动状态枚举（与原版项目相同）
    enum MovementState {
        ENTERING_SCREEN,   // 从屏幕外进入
        IDLE_AT_START,     // 在起始位置停留
        MOVING_RIGHT,      // 向右移动
        EXITING_SCREEN,    // 退出屏幕
        WAITING_OFF_SCREEN // 在屏幕外等待
    };

    int16_t x, y;
    int16_t startX;      // 起始X位置
    int16_t targetX;     // 目标X位置
    int16_t offScreenX;  // 屏幕外位置

    SpriteType currentSpriteType;
    MovementState movementState;
    const uint16_t* currentSprite;

    unsigned long lastSpriteChange;
    unsigned long lastMovementUpdate;
    unsigned long stateStartTime;

    // 动画参数（与原版项目相同）
    static const unsigned long SPRITE_ANIMATION_INTERVAL = 350; // 刺猬精灵切换间隔
    static const unsigned long MOVEMENT_UPDATE_INTERVAL = 150;  // 位置更新间隔
    static const unsigned long IDLE_DURATION = 2000;           // 停留时间
    static const unsigned long OFF_SCREEN_WAIT_DURATION = 3000; // 屏幕外等待时间
    static const int MOVEMENT_DISTANCE = 75;                   // 移动距离（原版25×3）
    static const int EXIT_DISTANCE = 60;                       // 退出屏幕的额外距离（原版20×3）
    static const int MOVEMENT_PACE = 3;                        // 每次移动3像素（原版1×3）

    void switchToNextSprite();
    void updateSpriteAnimation();
    void drawScaledSprite(int16_t x, int16_t y, const uint16_t* sprite);
    void redrawBackground(int16_t x, int16_t y, int16_t w, int16_t h);
};

// BOSS1精灵类
class Boss1 {
public:
    Boss1(int16_t x, int16_t y);
    void init();
    void draw();

    // 获取位置和尺寸
    int16_t getX() const { return x; }
    int16_t getY() const { return y; }
    int16_t getWidth() const { return BOSS1_SIZE[0]; }   // 32像素，不需要缩放
    int16_t getHeight() const { return BOSS1_SIZE[1]; }  // 32像素，不需要缩放

private:
    int16_t x, y;

    void drawSprite(int16_t x, int16_t y);
};

#endif // MARIO_THEME_H