#include "mario_theme.h"
#include "Arduino_GFX_Library.h"
#include "mario_assets.h"
#include "wifi_manager.h"
#include <esp_heap_caps.h>

extern Arduino_GFX *gfx;
extern int currentHour;     // 从 MarioClock.ino 引用
extern int currentMinute;   // 从 MarioClock.ino 引用
extern HWCDC USBSerial;     // 添加调试输出
extern WifiManager wifiMgr; // WiFi管理器实例

// 使用ESP32的内置psramFound函数，完全注释掉自定义版本
// bool psramFound() {
//     return ESP.getPsramSize() > 0;
// }

// 新增：声明用于双缓冲的虚拟缓冲区
uint16_t *frameBuffer = nullptr;
bool useFrameBuffer = false;

// 添加变量跟踪脏区域
int16_t dirtyX1 = VIRTUAL_WIDTH;
int16_t dirtyY1 = VIRTUAL_HEIGHT;
int16_t dirtyX2 = 0;
int16_t dirtyY2 = 0;
bool frameBufferDirty = false;

// 定义MAX宏用于获取两个值中的较大值
#ifndef MAX
#define MAX(a, b) ((a) > (b) ? (a) : (b))
#endif

// 定义MIN宏用于获取两个值中的较小值
#ifndef MIN
#define MIN(a, b) ((a) < (b) ? (a) : (b))
#endif

// 重写：使用简单可靠的字体绘制函数（添加调试）
void drawTextToFrameBuffer(String text, int16_t x, int16_t y, uint16_t color, int scale) {
    if (!useFrameBuffer || frameBuffer == nullptr) return;

    // 添加调试输出
    // USBSerial.print("绘制文本: ");
    // USBSerial.print(text);
    // USBSerial.print(" 位置: (");
    // USBSerial.print(x);
    // USBSerial.print(",");
    // USBSerial.print(y);
    // USBSerial.print(") 缩放: ");
    // USBSerial.println(scale);

    // 使用简单清晰的8x8像素字体数据（数字0-9）
    const uint8_t font8x8[10][8] = {
        {0x3C, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66, 0x3C}, // 0
        {0x0C, 0x1C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x3F}, // 1 - 修复反向问题
        {0x3C, 0x66, 0x60, 0x30, 0x18, 0x0C, 0x06, 0x7E}, // 2
        {0x3C, 0x66, 0x60, 0x38, 0x60, 0x60, 0x66, 0x3C}, // 3
        {0x30, 0x38, 0x3C, 0x36, 0x33, 0x7F, 0x30, 0x30}, // 4
        {0x7E, 0x06, 0x06, 0x3E, 0x60, 0x60, 0x66, 0x3C}, // 5
        {0x38, 0x0C, 0x06, 0x3E, 0x66, 0x66, 0x66, 0x3C}, // 6
        {0x7E, 0x60, 0x30, 0x18, 0x0C, 0x06, 0x06, 0x06}, // 7
        {0x3C, 0x66, 0x66, 0x3C, 0x66, 0x66, 0x66, 0x3C}, // 8
        {0x3C, 0x66, 0x66, 0x66, 0x7C, 0x60, 0x30, 0x1C}  // 9
    };

    for (int charIndex = 0; charIndex < text.length(); charIndex++) {
        char c = text.charAt(charIndex);
        if (c >= '0' && c <= '9') {
            int digitIndex = c - '0';

            // 计算字符起始位置 - 还原为3倍缩放
            int16_t charStartX = x + charIndex * 8 * scale; // 8像素宽度

            // USBSerial.print("字符 ");
            // USBSerial.print(c);
            // USBSerial.print(" 起始位置: ");
            // USBSerial.println(charStartX);

            // 绘制8x8字符
            for (int row = 0; row < 8; row++) {
                uint8_t rowData = font8x8[digitIndex][row];

                for (int col = 0; col < 8; col++) {
                    // 对数字1使用特殊的位读取方式
                    bool shouldDraw = false;
                    if (digitIndex == 1) {
                        // 数字1：从最高位开始读取
                        shouldDraw = (rowData & (0x80 >> col));
                    } else {
                        // 其他数字：从最低位开始读取
                        shouldDraw = (rowData & (1 << col));
                    }

                    if (shouldDraw) {
                        // 绘制缩放后的像素块
                        for (int sy = 0; sy < scale; sy++) {
                            for (int sx = 0; sx < scale; sx++) {
                                int16_t px = charStartX + col * scale + sx;
                                int16_t py = y + row * scale + sy;

                                // 边界检查并绘制
                                if (px >= 0 && px < VIRTUAL_WIDTH && py >= 0 && py < VIRTUAL_HEIGHT) {
                                    frameBuffer[py * VIRTUAL_WIDTH + px] = color;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

// 新增：脏区域管理函数
void markDirtyRegion(int16_t x, int16_t y, int16_t w, int16_t h) {
    if (useFrameBuffer && frameBuffer != nullptr) {
        int16_t x1 = MAX(0, x);
        int16_t y1 = MAX(0, y);
        int16_t x2 = MIN(VIRTUAL_WIDTH - 1, x + w - 1);
        int16_t y2 = MIN(VIRTUAL_HEIGHT - 1, y + h - 1);

        if (x1 <= x2 && y1 <= y2) {
            dirtyX1 = MIN(dirtyX1, x1);
            dirtyY1 = MIN(dirtyY1, y1);
            dirtyX2 = MAX(dirtyX2, x2);
            dirtyY2 = MAX(dirtyY2, y2);
            frameBufferDirty = true;
        }
    }
}

void resetDirtyRegion() {
    dirtyX1 = VIRTUAL_WIDTH;
    dirtyY1 = VIRTUAL_HEIGHT;
    dirtyX2 = 0;
    dirtyY2 = 0;
    frameBufferDirty = false;
}

// 优化的renderFrameBuffer函数，使用DMA和更高效的脏矩阵更新
void renderFrameBuffer() {
    if (frameBuffer != nullptr && useFrameBuffer && frameBufferDirty) {
        // 确保脏区域有效
        if (dirtyX1 <= dirtyX2 && dirtyY1 <= dirtyY2) {
            // 计算脏区域宽高
            int16_t dirtyWidth = dirtyX2 - dirtyX1 + 1;
            int16_t dirtyHeight = dirtyY2 - dirtyY1 + 1;

            // 使用startWrite/endWrite包装以启用DMA传输
            gfx->startWrite();

            // 对于小区域，使用像素级更新；对于大区域，使用块传输
            if (dirtyWidth * dirtyHeight < 100) {
                // 小区域：逐像素更新，减少传输开销
                for (int16_t y = dirtyY1; y <= dirtyY2; y++) {
                    for (int16_t x = dirtyX1; x <= dirtyX2; x++) {
                        uint16_t color = frameBuffer[y * VIRTUAL_WIDTH + x];
                        gfx->writePixel(OFFSET_X + x, OFFSET_Y + y, color);
                    }
                }
            } else {
                // 大区域：使用水平线段优化传输
                for (int16_t y = dirtyY1; y <= dirtyY2; y++) {
                    int16_t startX = dirtyX1;
                    uint16_t currentColor = frameBuffer[y * VIRTUAL_WIDTH + startX];

                    for (int16_t x = dirtyX1 + 1; x <= dirtyX2; x++) {
                        uint16_t newColor = frameBuffer[y * VIRTUAL_WIDTH + x];

                        // 如果颜色变化，绘制之前的线段并开始新的线段
                        if (newColor != currentColor) {
                            gfx->writeFastHLine(OFFSET_X + startX, OFFSET_Y + y, x - startX, currentColor);
                            startX = x;
                            currentColor = newColor;
                        }
                    }

                    // 绘制最后一个线段
                    gfx->writeFastHLine(OFFSET_X + startX, OFFSET_Y + y, dirtyX2 - startX + 1, currentColor);
                }
            }

            gfx->endWrite();

            // 重置脏区域
            dirtyX1 = VIRTUAL_WIDTH;
            dirtyY1 = VIRTUAL_HEIGHT;
            dirtyX2 = 0;
            dirtyY2 = 0;
            frameBufferDirty = false;
        }
    }
}



// Mario类实现
Mario::Mario() {
    x = MARIO_START_X;
    y = MARIO_START_Y;
    jumpHeight = 0;
    isJumping = false;
    frame = 0;
    state = STANDING;
    lastUpdate = 0;
    currentWidth = MARIO_IDLE_SIZE[0];
    currentHeight = MARIO_IDLE_SIZE[1];
}

// 新增：AABB碰撞检测
bool Mario::collidedWith(int16_t bx, int16_t by, int16_t bwidth, int16_t bheight) const {
    return (x < bx + bwidth &&
            x + getWidth() > bx &&
            y < by + bheight &&
            y + getHeight() > by);
}

// 新增：强制下落
void Mario::forceFall() {
    if (isJumping && jumpHeight < MARIO_JUMP_HEIGHT) {
        jumpHeight = MARIO_JUMP_HEIGHT; // 直接进入下落阶段
    }
}

// 新增：初始化帧缓冲区
bool initFrameBuffer() {
    if (frameBuffer == nullptr) {
        // 检查是否有PSRAM可用
        if (psramFound()) {
            // 为整个虚拟屏幕区域创建缓冲区
            frameBuffer = (uint16_t*)ps_malloc(VIRTUAL_WIDTH * VIRTUAL_HEIGHT * sizeof(uint16_t));
        } else {
            // 如果没有PSRAM，尝试使用普通内存
            frameBuffer = (uint16_t*)malloc(VIRTUAL_WIDTH * VIRTUAL_HEIGHT * sizeof(uint16_t));
        }

        if (frameBuffer == nullptr) {
            return false;
        }

        // 初始化为天空颜色
        for (int i = 0; i < VIRTUAL_WIDTH * VIRTUAL_HEIGHT; i++) {
            frameBuffer[i] = SKY_COLOR;
        }
        useFrameBuffer = true;

        // 初始化脏区域
        dirtyX1 = VIRTUAL_WIDTH;
        dirtyY1 = VIRTUAL_HEIGHT;
        dirtyX2 = 0;
        dirtyY2 = 0;
        frameBufferDirty = false;
    }
    return true;
}

// 新增：释放马里奥时钟帧缓冲区
void cleanupMarioFrameBuffer() {
    if (frameBuffer != nullptr) {
        free(frameBuffer);
        frameBuffer = nullptr;
        useFrameBuffer = false;
        USBSerial.println("马里奥时钟帧缓冲区已释放");
    }
}

// 修改代码，使用外部定义的drawPixelVirtual和fillRectVirtual函数
// 外部drawPixelVirtual和fillRectVirtual函数的简单包装器，用于更新脏区域
void updatePixelVirtual(int16_t x, int16_t y, uint16_t color) {
    if (x >= 0 && x < VIRTUAL_WIDTH && y >= 0 && y < VIRTUAL_HEIGHT) {
        if (useFrameBuffer && frameBuffer != nullptr) {
            // 获取旧颜色
            uint16_t oldColor = frameBuffer[y * VIRTUAL_WIDTH + x];

            // 如果颜色改变了，更新脏区域
            if (oldColor != color) {
                // 更新缓冲区
                frameBuffer[y * VIRTUAL_WIDTH + x] = color;

                // 使用新的脏区域管理函数
                markDirtyRegion(x, y, 1, 1);
            }
        } else {
            // 直接使用外部函数绘制到屏幕
            drawPixelVirtual(x, y, color);
        }
    }
}

void updateRectVirtual(int16_t x, int16_t y, int16_t w, int16_t h, uint16_t color) {
    if (x < VIRTUAL_WIDTH && y < VIRTUAL_HEIGHT && x + w > 0 && y + h > 0) {
        int16_t x1 = (x > 0) ? x : 0;
        int16_t y1 = (y > 0) ? y : 0;
        int16_t x2 = (x + w < VIRTUAL_WIDTH) ? (x + w) : VIRTUAL_WIDTH;
        int16_t y2 = (y + h < VIRTUAL_HEIGHT) ? (y + h) : VIRTUAL_HEIGHT;

        if (useFrameBuffer && frameBuffer != nullptr) {
            // 使用新的脏区域管理函数
            markDirtyRegion(x1, y1, x2 - x1, y2 - y1);

            // 绘制到帧缓冲区
            for (int16_t j = y1; j < y2; j++) {
                for (int16_t i = x1; i < x2; i++) {
                    frameBuffer[j * VIRTUAL_WIDTH + i] = color;
                }
            }
        } else {
            // 直接使用外部函数绘制到屏幕
            fillRectVirtual(x, y, w, h, color);
        }
    }
}

// 修改：drawScaledBitmap函数以支持双缓冲
void drawScaledBitmap(int16_t x, int16_t y, const uint16_t* bitmap, int16_t w, int16_t h, int scale) {
    for (int16_t j = 0; j < h; j++) {
        for (int16_t i = 0; i < w; i++) {
            uint16_t color = bitmap[j * w + i];
            if (color != _MASK) { // 只绘制非透明像素
                for (int16_t sy = 0; sy < scale; sy++) {
                    for (int16_t sx = 0; sx < scale; sx++) {
                        if (useFrameBuffer && frameBuffer != nullptr) {
                            // 绘制到帧缓冲区
                            int16_t px = x + (i * scale) + sx;
                            int16_t py = y + (j * scale) + sy;
                            if (px >= 0 && px < VIRTUAL_WIDTH && py >= 0 && py < VIRTUAL_HEIGHT) {
                                frameBuffer[py * VIRTUAL_WIDTH + px] = color;
                                // 更新脏区域
                                dirtyX1 = MIN(dirtyX1, px);
                                dirtyY1 = MIN(dirtyY1, py);
                                dirtyX2 = MAX(dirtyX2, px);
                                dirtyY2 = MAX(dirtyY2, py);
                                frameBufferDirty = true;
                            }
                        } else {
                            // 直接绘制到屏幕
                            gfx->drawPixel(OFFSET_X + x + (i * scale) + sx,
                                         OFFSET_Y + y + (j * scale) + sy,
                                         color);
                        }
                    }
                }
            }
        }
    }
}

// 修改Mario::init()函数
void Mario::init() {
    // 绘制静止状态的马里奥 - 3倍缩放
    drawScaledBitmap(x, y, MARIO_IDLE, MARIO_IDLE_SIZE[0], MARIO_IDLE_SIZE[1], 3);
    currentWidth = MARIO_IDLE_SIZE[0] * 3;
    currentHeight = MARIO_IDLE_SIZE[1] * 3;
}

// 修改Mario::draw()函数
void Mario::draw() {
    // 直接绘制当前状态的马里奥，不做任何清除 - 3倍缩放
    if (isJumping) {
        currentWidth = MARIO_JUMP_SIZE[0] * 3;
        currentHeight = MARIO_JUMP_SIZE[1] * 3;
        drawScaledBitmap(x, y, MARIO_JUMP, MARIO_JUMP_SIZE[0], MARIO_JUMP_SIZE[1], 3);
    } else {
        currentWidth = MARIO_IDLE_SIZE[0] * 3;
        currentHeight = MARIO_IDLE_SIZE[1] * 3;
        drawScaledBitmap(x, y, MARIO_IDLE, MARIO_IDLE_SIZE[0], MARIO_IDLE_SIZE[1], 3);
    }
}

// 修改Mario::jump()函数
void Mario::jump() {
    if (!isJumping) {
        // 保存当前的精灵尺寸和位置
        uint8_t oldWidth = currentWidth;
        uint8_t oldHeight = currentHeight;
        int16_t oldX = x;
        int16_t oldY = y;

        // 切换到跳跃状态
        isJumping = true;
        jumpHeight = 0;

        // 性能优化：暂停云朵动画以减少渲染冲突
        extern Scene scene;
        scene.pauseCloudAnimation();

        // 更新精灵尺寸为跳跃精灵
        currentWidth = MARIO_JUMP_SIZE[0] * 3;
        currentHeight = MARIO_JUMP_SIZE[1] * 3;

        // 绘制新的跳跃马里奥精灵（位置稍微上移一点，使动作更流畅）
        int16_t initialJumpOffset = 2; // 初始跳跃时稍微上移
        y -= initialJumpOffset;
        jumpHeight += initialJumpOffset;

        // 获取方块和场景引用以便重绘
        extern Block hourBlock;
        extern Block minuteBlock;

        // 原子操作：在双缓冲模式下一次性完成所有绘制操作
        if (useFrameBuffer) {
            // 在帧缓冲区中进行原子操作，避免时序间隙

            // 1. 清除旧的静止马里奥精灵区域
            updateRectVirtual(oldX, oldY, oldWidth, oldHeight, SKY_COLOR);

            // 2. 重绘被清除区域内的云朵部分
            scene.redrawCloudsInRegion(oldX, oldY, oldWidth, oldHeight);

            // 3. 检查并重绘可能被清除区域覆盖的方块
            if (hourBlock.getState() == IDLE &&
                oldX < hourBlock.getX() + hourBlock.getWidth() &&
                oldX + oldWidth > hourBlock.getX() &&
                oldY < hourBlock.getY() + hourBlock.getHeight() &&
                oldY + oldHeight > hourBlock.getY()) {
                hourBlock.draw();
            }

            if (minuteBlock.getState() == IDLE &&
                oldX < minuteBlock.getX() + minuteBlock.getWidth() &&
                oldX + oldWidth > minuteBlock.getX() &&
                oldY < minuteBlock.getY() + minuteBlock.getHeight() &&
                oldY + oldHeight > minuteBlock.getY()) {
                minuteBlock.draw();
            }

            // 4. 绘制新的跳跃马里奥精灵
            drawScaledBitmap(x, y, MARIO_JUMP, MARIO_JUMP_SIZE[0], MARIO_JUMP_SIZE[1], 3);

            // 5. 一次性渲染所有更改到屏幕，消除时序间隙
            renderFrameBuffer();
        } else {
            // 非双缓冲模式：使用startWrite/endWrite包装原子操作
            gfx->startWrite();

            // 1. 清除旧的静止马里奥精灵
            gfx->fillRect(OFFSET_X + oldX, OFFSET_Y + oldY, oldWidth, oldHeight, SKY_COLOR);

            // 2. 重绘被清除区域内的云朵部分
            scene.redrawCloudsInRegion(oldX, oldY, oldWidth, oldHeight);

            // 3. 检查并重绘可能被清除区域覆盖的方块
            if (hourBlock.getState() == IDLE &&
                oldX < hourBlock.getX() + hourBlock.getWidth() &&
                oldX + oldWidth > hourBlock.getX() &&
                oldY < hourBlock.getY() + hourBlock.getHeight() &&
                oldY + oldHeight > hourBlock.getY()) {
                hourBlock.draw();
            }

            if (minuteBlock.getState() == IDLE &&
                oldX < minuteBlock.getX() + minuteBlock.getWidth() &&
                oldX + oldWidth > minuteBlock.getX() &&
                oldY < minuteBlock.getY() + minuteBlock.getHeight() &&
                oldY + oldHeight > minuteBlock.getY()) {
                minuteBlock.draw();
            }

            // 4. 绘制新的跳跃马里奥精灵
            drawScaledBitmap(x, y, MARIO_JUMP, MARIO_JUMP_SIZE[0], MARIO_JUMP_SIZE[1], 3);

            gfx->endWrite();
        }

        // 确保更新时间戳，使动画立即开始
        lastUpdate = millis() - 50; // 减去50ms使下一帧立即更新
    }
}

// 修改Mario::update()函数
void Mario::update() {
    unsigned long currentTime = millis();
    if (currentTime - lastUpdate > 40) { // 动画更新间隔调整为40ms，使动画更流畅
        if (isJumping) {
            // 保存旧位置
            int16_t oldX = x;
            int16_t oldY = y;

            // 更新位置
            if (jumpHeight < MARIO_JUMP_HEIGHT) { // 上升
                // 调整跳跃速度，使用可变速度模拟物理效果
                int16_t jumpSpeed = 3;
                if (jumpHeight > MARIO_JUMP_HEIGHT * 0.7) {
                    jumpSpeed = 2; // 接近顶点时减速
                }
                y -= jumpSpeed;
                jumpHeight += jumpSpeed;
            } else { // 下降
                // 下落速度可以稍快一些
                y += 3;
            }

            // --- 获取方块引用 ---
            extern Block hourBlock;
            extern Block minuteBlock;
            extern int currentHour;
            extern int currentMinute;
            extern int lastHour;
            extern int lastMinute;
            extern bool isRealTimeUpdate;

            // 获取场景引用以便重绘云朵
            extern Scene scene;

            // 原子操作：清除旧位置并重绘背景，避免时序间隙
            if (useFrameBuffer) {
                // 双缓冲模式：在帧缓冲区中进行原子操作

                // 1. 完全清除旧位置的马里奥图像
                updateRectVirtual(oldX, oldY, currentWidth, currentHeight, SKY_COLOR);

                // 2. 重绘被清除区域内的云朵部分
                scene.redrawCloudsInRegion(oldX, oldY, currentWidth, currentHeight);

                // 3. 检查并重绘可能被清除区域覆盖的方块
                if (hourBlock.getState() == IDLE &&
                    oldX < hourBlock.getX() + hourBlock.getWidth() &&
                    oldX + currentWidth > hourBlock.getX() &&
                    oldY < hourBlock.getY() + hourBlock.getHeight() &&
                    oldY + currentHeight > hourBlock.getY()) {
                    hourBlock.draw();
                }

                if (minuteBlock.getState() == IDLE &&
                    oldX < minuteBlock.getX() + minuteBlock.getWidth() &&
                    oldX + currentWidth > minuteBlock.getX() &&
                    oldY < minuteBlock.getY() + minuteBlock.getHeight() &&
                    oldY + currentHeight > minuteBlock.getY()) {
                    minuteBlock.draw();
                }
            } else {
                // 非双缓冲模式：使用startWrite/endWrite包装原子操作
                gfx->startWrite();

                // 1. 清除旧位置
                gfx->fillRect(OFFSET_X + oldX, OFFSET_Y + oldY, currentWidth, currentHeight, SKY_COLOR);

                // 2. 重绘被清除区域内的云朵部分
                scene.redrawCloudsInRegion(oldX, oldY, currentWidth, currentHeight);

                // 3. 检查并重绘可能被清除区域覆盖的方块
                if (hourBlock.getState() == IDLE &&
                    oldX < hourBlock.getX() + hourBlock.getWidth() &&
                    oldX + currentWidth > hourBlock.getX() &&
                    oldY < hourBlock.getY() + hourBlock.getHeight() &&
                    oldY + currentHeight > hourBlock.getY()) {
                    hourBlock.draw();
                }

                if (minuteBlock.getState() == IDLE &&
                    oldX < minuteBlock.getX() + minuteBlock.getWidth() &&
                    oldX + currentWidth > minuteBlock.getX() &&
                    oldY < minuteBlock.getY() + minuteBlock.getHeight() &&
                    oldY + currentHeight > minuteBlock.getY()) {
                    minuteBlock.draw();
                }

                gfx->endWrite();
            }

            // --- 检测和方块的碰撞 ---
            bool collidedThisFrame = false;

            // 新逻辑：根据当前时间判断应该弹起哪些方块
            // 如果是整点（分钟为0），两个方块都弹起
            // 如果不是整点，只有分钟方块弹起
            bool shouldHitHourBlock = (currentMinute == 0);  // 整点时弹起小时方块
            bool shouldHitMinuteBlock = true;                // 分钟方块总是弹起

            // 马里奥时钟模式下的方块弹跳逻辑
            // 1. 检测小时方块
            if (hourBlock.getState() == IDLE &&
                this->collidedWith(hourBlock.getX(), hourBlock.getY(),
                                 hourBlock.getWidth(), hourBlock.getHeight()) &&
                shouldHitHourBlock) {
                hourBlock.hit();
                // 简化版本：总是更新文本
                char hourStr[3];
                sprintf(hourStr, "%d", currentHour);
                hourBlock.setText(String(hourStr));
                collidedThisFrame = true;
            }

            // 2. 检测分钟方块
            if (minuteBlock.getState() == IDLE &&
                this->collidedWith(minuteBlock.getX(), minuteBlock.getY(),
                                 minuteBlock.getWidth(), minuteBlock.getHeight()) &&
                shouldHitMinuteBlock) {
                minuteBlock.hit();
                // 简化版本：总是更新文本
                char minuteStr[3];
                sprintf(minuteStr, "%02d", currentMinute);
                minuteBlock.setText(String(minuteStr));
                collidedThisFrame = true;
            }

            if (collidedThisFrame) {
                this->forceFall();
            }
            // --- END ---

            // 3. 绘制马里奥 - 在原子操作中完成
            if (useFrameBuffer) {
                // 双缓冲模式：直接绘制到帧缓冲区
                if (y >= MARIO_START_Y) {
                    // 如果回到地面，切换到静止状态
                    y = MARIO_START_Y;
                    isJumping = false;
                    jumpHeight = 0;

                    // 重置时间更新标志
                    extern bool isRealTimeUpdate;
                    isRealTimeUpdate = false;

                    // 性能优化：恢复云朵动画
                    extern Scene scene;
                    scene.resumeCloudAnimation();

                    // 切换到静止精灵
                    currentWidth = MARIO_IDLE_SIZE[0] * 3;
                    currentHeight = MARIO_IDLE_SIZE[1] * 3;
                    drawScaledBitmap(x, y, MARIO_IDLE, MARIO_IDLE_SIZE[0], MARIO_IDLE_SIZE[1], 3);
                } else {
                    // 仍在空中，使用跳跃精灵
                    currentWidth = MARIO_JUMP_SIZE[0] * 3;
                    currentHeight = MARIO_JUMP_SIZE[1] * 3;
                    drawScaledBitmap(x, y, MARIO_JUMP, MARIO_JUMP_SIZE[0], MARIO_JUMP_SIZE[1], 3);
                }

                // 一次性渲染所有更改到屏幕
                renderFrameBuffer();
            } else {
                // 非双缓冲模式：在startWrite/endWrite中完成绘制
                gfx->startWrite();

                if (y >= MARIO_START_Y) {
                    // 如果回到地面，切换到静止状态
                    y = MARIO_START_Y;
                    isJumping = false;
                    jumpHeight = 0;

                    // 重置时间更新标志
                    extern bool isRealTimeUpdate;
                    isRealTimeUpdate = false;

                    // 性能优化：恢复云朵动画
                    extern Scene scene;
                    scene.resumeCloudAnimation();

                    // 切换到静止精灵
                    currentWidth = MARIO_IDLE_SIZE[0] * 3;
                    currentHeight = MARIO_IDLE_SIZE[1] * 3;
                    drawScaledBitmap(x, y, MARIO_IDLE, MARIO_IDLE_SIZE[0], MARIO_IDLE_SIZE[1], 3);
                } else {
                    // 仍在空中，使用跳跃精灵
                    currentWidth = MARIO_JUMP_SIZE[0] * 3;
                    currentHeight = MARIO_JUMP_SIZE[1] * 3;
                    drawScaledBitmap(x, y, MARIO_JUMP, MARIO_JUMP_SIZE[0], MARIO_JUMP_SIZE[1], 3);
                }

                gfx->endWrite();
            }

            lastUpdate = currentTime;
        }
    }
}

// Block类实现
Block::Block(int16_t _x, int16_t _y) {
    x = _x;
    y = _y;
    firstY = _y;
    state = IDLE;
    lastState = IDLE;
    text = "";
    lastUpdate = 0;
}

// 新增：AABB碰撞检测
bool Block::collidedWith(const Mario& mario) const {
    return (x < mario.getX() + mario.getWidth() &&
            x + getWidth() > mario.getX() &&
            y < mario.getY() + mario.getHeight() &&
            y + getHeight() > mario.getY());
}

// 修改Block::init()函数
void Block::init() {
    // 使用新的颜色控制方法绘制问号方块
    uint16_t blockColor = getCurrentColor();
    drawBlockWithColor(blockColor);
    setTextBlock();
}

void Block::idle() {
    if (state != IDLE) {
        lastState = state;
        state = IDLE;
        y = firstY;
    }
}

void Block::hit() {
    if (state != HIT) {
        lastState = state;
        state = HIT;
        lastY = y;
        direction = UP;
    }
}

// 还原为3倍缩放的字体显示
void Block::setTextBlock() {
    if (useFrameBuffer && frameBuffer != nullptr) {
        // 还原为3倍缩放，调整位置
        int16_t textVirtualX, textVirtualY;

        if (text.length() == 1) {
            // 单个数字：方块中央偏移 - 3倍缩放
            textVirtualX = x + 16; // 还原为适应3倍缩放
            textVirtualY = y + 16; // 还原为适应3倍缩放
        } else {
            // 两个数字：稍微左移 - 3倍缩放
            textVirtualX = x + 4;  // 还原以容纳两个字符
            textVirtualY = y + 16; // 垂直居中
        }

        // USBSerial.print("方块位置: (");
        // USBSerial.print(x);
        // USBSerial.print(",");
        // USBSerial.print(y);
        // USBSerial.print(") 文本: ");
        // USBSerial.print(text);
        // USBSerial.print(" 文本位置: (");
        // USBSerial.print(textVirtualX);
        // USBSerial.print(",");
        // USBSerial.print(textVirtualY);
        // USBSerial.println(")");

        // 还原为3倍缩放绘制文本
        drawTextToFrameBuffer(text, textVirtualX, textVirtualY, BLACK, 3);

        // 标记整个方块区域为脏区域（简化版本）
        markDirtyRegion(x, y, BLOCK_WIDTH, BLOCK_HEIGHT);
    } else {
        // 不使用双缓冲时，直接绘制到屏幕（保持兼容性）
        gfx->setTextColor(BLACK);
        gfx->setTextSize(3); // 还原为3倍缩放

        if (text.length() == 1) {
            gfx->setCursor(OFFSET_X + x + 21, OFFSET_Y + y + 18);
        } else {
            gfx->setCursor(OFFSET_X + x + 12, OFFSET_Y + y + 18);
        }

        gfx->print(text);
        gfx->setTextSize(1);
    }
}

void Block::update() {
    // 简化版本：只显示当前时间
    displayCurrentTime();

    if (state == IDLE && lastState != state) {
        // 当状态从HIT变为IDLE时，重绘方块
        uint16_t blockColor = getCurrentColor();
        drawBlockWithColor(blockColor);
        setTextBlock();
        lastState = state;

        // 如果使用双缓冲，渲染到屏幕
        if (useFrameBuffer) {
            renderFrameBuffer();
        }
    } else if (state == HIT) {
        if (millis() - lastUpdate >= 60) {
            // 保存旧位置
            int16_t oldY = y;

            // 更新位置
            y = y + (MOVE_PACE * (direction == UP ? -1 : 1));

            // 计算需要清除的区域（只清除移动方向上的边缘）
            int16_t clearX, clearY, clearW, clearH;
            if (useFrameBuffer) {
                // 使用双缓冲时通过updateRectVirtual清除
                if (direction == UP) {
                    // 向上移动，清除底部边缘
                    clearX = x;
                    clearY = oldY + BLOCK_HEIGHT - MOVE_PACE;
                    clearW = BLOCK_WIDTH;
                    clearH = MOVE_PACE;
                    updateRectVirtual(clearX, clearY, clearW, clearH, SKY_COLOR);
                } else {
                    // 向下移动，清除顶部边缘
                    clearX = x;
                    clearY = oldY;
                    clearW = BLOCK_WIDTH;
                    clearH = MOVE_PACE;
                    updateRectVirtual(clearX, clearY, clearW, clearH, SKY_COLOR);
                }
            } else {
                // 不使用双缓冲时直接清除
                if (direction == UP) {
                    // 向上移动，清除底部边缘
                    clearX = x;
                    clearY = oldY + BLOCK_HEIGHT - MOVE_PACE;
                    clearW = BLOCK_WIDTH;
                    clearH = MOVE_PACE;
                    fillRectVirtual(clearX, clearY, clearW, clearH, SKY_COLOR);
                } else {
                    // 向下移动，清除顶部边缘
                    clearX = x;
                    clearY = oldY;
                    clearW = BLOCK_WIDTH;
                    clearH = MOVE_PACE;
                    fillRectVirtual(clearX, clearY, clearW, clearH, SKY_COLOR);
                }
            }

            // 获取场景引用以便重绘云朵
            extern Scene scene;

            // 重绘被清除区域内的云朵部分
            scene.redrawCloudsInRegion(clearX, clearY, clearW, clearH);

            // 绘制方块
            uint16_t blockColor = getCurrentColor();
            drawBlockWithColor(blockColor);

            setTextBlock();

            if ((firstY - y) >= MAX_MOVE_HEIGHT) {
                direction = DOWN;
            }

            if (y >= firstY && direction == DOWN) {
                idle();
            }

            lastUpdate = millis();

            // 如果使用双缓冲，渲染到屏幕
            if (useFrameBuffer) {
                renderFrameBuffer();
            }
        }
    }
}

void Block::draw() {
    // 使用新的颜色控制方法绘制问号方块
    uint16_t blockColor = getCurrentColor();
    drawBlockWithColor(blockColor);
    setTextBlock();
}

void Block::setText(String _text) {
    if (text != _text) {
        text = _text;
        // 如果方块处于 IDLE 状态，我们需要立即重绘它以显示新文本，
        // 而不播放 hit() 动画。
        if (state == IDLE) {
            if (useFrameBuffer && frameBuffer != nullptr) {
                // 使用双缓冲时，完全在帧缓冲区操作，避免屏闪
                // 计算文本区域在虚拟坐标系中的位置
                int16_t textVirtualX, textVirtualY;
                uint16_t textW, textH;

                // 根据文本长度确定位置 - 还原为3倍缩放
                if (text.length() == 1) {
                    // 单个数字：居中显示
                    textVirtualX = x + (BLOCK_WIDTH - 24) / 2; // 8像素 * 3倍缩放
                    textVirtualY = y + (BLOCK_HEIGHT - 24) / 2; // 8像素 * 3倍缩放
                    textW = 24; // 单个字符宽度
                } else {
                    // 两个数字：需要考虑间距
                    int totalWidth = 2 * 24; // 两个字符的总宽度 (8*3 * 2个字符)
                    textVirtualX = x + (BLOCK_WIDTH - totalWidth) / 2;
                    textVirtualY = y + (BLOCK_HEIGHT - 24) / 2;
                    textW = totalWidth; // 两个字符的总宽度
                }
                textH = 24; // 文本的近似高度 (8*3)

                // 清除帧缓冲区中的文本区域
                for (int16_t py = textVirtualY - 2; py < textVirtualY + textH + 2; py++) {
                    for (int16_t px = textVirtualX - 2; px < textVirtualX + textW + 2; px++) {
                        if (px >= 0 && px < VIRTUAL_WIDTH && py >= 0 && py < VIRTUAL_HEIGHT) {
                            frameBuffer[py * VIRTUAL_WIDTH + px] = SKY_COLOR;
                        }
                    }
                }

                // 获取场景引用以便重绘云朵
                extern Scene scene;

                // 重绘被清除区域内的云朵部分
                scene.redrawCloudsInRegion(textVirtualX - 2, textVirtualY - 2, textW + 4, textH + 4);

                // 重绘被清除区域的方块部分
                uint16_t blockColor = getCurrentColor();
                for (int i = 0; i < BLOCK_HEIGHT; i++) {
                    for (int j = 0; j < BLOCK_WIDTH; j++) {
                        int16_t pixelX = x + j;
                        int16_t pixelY = y + i;
                        if (pixelX >= textVirtualX - 2 && pixelX < textVirtualX + textW + 2 &&
                            pixelY >= textVirtualY - 2 && pixelY < textVirtualY + textH + 2) {
                            int i_src = i * 19 / BLOCK_HEIGHT;
                            int j_src = j * 19 / BLOCK_WIDTH;
                            uint16_t pixel = BLOCK[i_src * 19 + j_src];
                            if (pixel != SKY_COLOR && pixelX >= 0 && pixelX < VIRTUAL_WIDTH &&
                                pixelY >= 0 && pixelY < VIRTUAL_HEIGHT) {
                                // 如果原始像素是方块颜色，则替换为当前颜色
                                if (pixel == 0xE4E4) {
                                    frameBuffer[pixelY * VIRTUAL_WIDTH + pixelX] = blockColor;
                                } else {
                                    frameBuffer[pixelY * VIRTUAL_WIDTH + pixelX] = pixel;
                                }
                            }
                        }
                    }
                }

                // 使用新的文本绘制函数直接绘制到帧缓冲区
                drawTextToFrameBuffer(text, textVirtualX, textVirtualY, BLACK, 3);

                // 标记脏区域
                markDirtyRegion(textVirtualX - 2, textVirtualY - 2, textW + 4, textH + 4);
            } else {
                // 不使用双缓冲时的原有逻辑（保持兼容性）
                // 计算文本区域的位置和大小
                int16_t textX, textY;
                uint16_t textW, textH;

                // 根据文本长度确定位置 - 还原为3倍缩放
                if (text.length() == 1) {
                    textX = OFFSET_X + x + 21;
                    textY = OFFSET_Y + y + 18;
                    textW = 18; // 单个数字的近似宽度 (6*3)
                } else {
                    textX = OFFSET_X + x + 12;
                    textY = OFFSET_Y + y + 18;
                    textW = 30; // 两个数字的近似宽度 (10*3)
                }
                textH = 24; // 文本的近似高度 (8*3)

                // 只清除文本区域
                gfx->fillRect(textX - 3, textY - 24, textW + 6, textH + 6, SKY_COLOR);

                // 获取场景引用以便重绘云朵
                extern Scene scene;

                // 重绘被清除区域内的云朵部分（转换为虚拟坐标）
                int16_t clearVirtualX = textX - 3 - OFFSET_X;
                int16_t clearVirtualY = textY - 24 - OFFSET_Y;
                int16_t clearVirtualW = textW + 6;
                int16_t clearVirtualH = textH + 6;
                scene.redrawCloudsInRegion(clearVirtualX, clearVirtualY, clearVirtualW, clearVirtualH);

                // 重绘被清除区域的方块部分
                uint16_t blockColor = getCurrentColor();
                for (int i = 0; i < BLOCK_HEIGHT; i++) {
                    for (int j = 0; j < BLOCK_WIDTH; j++) {
                        // 检查像素是否在文本区域内
                        int16_t pixelX = OFFSET_X + x + j;
                        int16_t pixelY = OFFSET_Y + y + i;
                        if (pixelX >= textX - 3 && pixelX < textX - 3 + textW + 6 &&
                            pixelY >= textY - 24 && pixelY < textY - 24 + textH + 6) {
                            int i_src = i * 19 / BLOCK_HEIGHT;
                            int j_src = j * 19 / BLOCK_WIDTH;
                            uint16_t pixel = BLOCK[i_src * 19 + j_src];
                            if (pixel != SKY_COLOR) {
                                // 如果原始像素是方块颜色，则替换为当前颜色
                                if (pixel == 0xE4E4) {
                                    gfx->drawPixel(pixelX, pixelY, blockColor);
                                } else {
                                    gfx->drawPixel(pixelX, pixelY, pixel);
                                }
                            }
                        }
                    }
                }

                // 重绘文本
                setTextBlock();
            }
        }
    }
}

BlockState Block::getState() const {
    return state;
}

// Scene类实现
Scene::Scene() {
    lastUpdate = 0;
    // Initialize CLOUD3 循环动画变量
    cloud3X = CLOUD3_LEFT_POS;           // CLOUD3 初始位置在左侧
    cloud3TargetX = CLOUD3_RIGHT_POS;    // 第一个目标是右侧位置
    cloudAnimationActive = true;         // 激活CLOUD3动画
    cloudState = MOVING_RIGHT;           // 初始状态：向右移动
    waitStartTime = 0;                   // 等待开始时间
    cloudAnimationPaused = false;        // 初始化暂停状态

    // Initialize 上方CLOUD3 完整循环动画变量
    cloud3UpperX = CLOUD3_UPPER_RIGHT_POS;          // 上方CLOUD3 初始位置在原cloud2位置(153)
    cloud3UpperTargetX = CLOUD3_UPPER_LEFT_POS;     // 第一个目标是最左侧位置(-39)
    cloud3UpperState = MOVING_LEFT;                 // 初始状态：向左移动
    cloud3UpperWaitStartTime = 0;                   // 等待开始时间
}

void Scene::update() {
    // 云朵循环动画更新
    unsigned long currentTime = millis();
    // 只有在动画激活且未暂停时才更新
    if (cloudAnimationActive && !cloudAnimationPaused && currentTime - lastUpdate > 100) { // 每100ms更新一次，实现慢速移动
        bool needsRedraw = false;

        switch (cloudState) {
            case MOVING_RIGHT:
                if (cloud3X < cloud3TargetX) {
                    // 智能清除：检查是否会影响时间方块，如果会则精确清除
                    extern Block hourBlock;
                    extern Block minuteBlock;

                    // 云朵旧位置区域
                    int16_t cloudOldX = cloud3X;
                    int16_t cloudOldY = 66;
                    int16_t cloudW = 26 * 3;
                    int16_t cloudH = 12 * 3;

                    // 检查是否与时间方块重叠
                    bool overlapHour = (hourBlock.getX() < cloudOldX + cloudW && hourBlock.getX() + hourBlock.getWidth() > cloudOldX &&
                                       hourBlock.getY() < cloudOldY + cloudH && hourBlock.getY() + hourBlock.getHeight() > cloudOldY);
                    bool overlapMinute = (minuteBlock.getX() < cloudOldX + cloudW && minuteBlock.getX() + minuteBlock.getWidth() > cloudOldX &&
                                         minuteBlock.getY() < cloudOldY + cloudH && minuteBlock.getY() + minuteBlock.getHeight() > cloudOldY);

                    if (overlapHour || overlapMinute) {
                        // 有重叠时，只清除云朵像素，保留时间方块像素
                        for (int j = 0; j < cloudH; j++) {
                            for (int i = 0; i < cloudW; i++) {
                                int16_t pixelX = cloudOldX + i;
                                int16_t pixelY = cloudOldY + j;

                                // 检查这个像素是否在时间方块区域内
                                bool inHourBlock = (overlapHour && pixelX >= hourBlock.getX() && pixelX < hourBlock.getX() + hourBlock.getWidth() &&
                                                   pixelY >= hourBlock.getY() && pixelY < hourBlock.getY() + hourBlock.getHeight());
                                bool inMinuteBlock = (overlapMinute && pixelX >= minuteBlock.getX() && pixelX < minuteBlock.getX() + minuteBlock.getWidth() &&
                                                     pixelY >= minuteBlock.getY() && pixelY < minuteBlock.getY() + minuteBlock.getHeight());

                                // 只清除不在时间方块内的像素
                                if (!inHourBlock && !inMinuteBlock) {
                                    updatePixelVirtual(pixelX, pixelY, SKY_COLOR);
                                }
                            }
                        }
                    } else {
                        // 无重叠时，正常清除整个区域
                        updateRectVirtual(cloudOldX, cloudOldY, cloudW, cloudH, SKY_COLOR);
                    }

                    // 向右移动 CLOUD3
                    cloud3X++;
                    needsRedraw = true;
                } else {
                    // 到达右侧，开始等待
                    cloudState = WAITING_RIGHT;
                    waitStartTime = currentTime;
                }
                break;

            case WAITING_RIGHT:
                if (currentTime - waitStartTime >= WAIT_DURATION) {
                    // 等待结束，开始向左移动
                    cloudState = MOVING_LEFT;
                    cloud3TargetX = CLOUD3_LEFT_POS;
                }
                break;

            case MOVING_LEFT:
                if (cloud3X > cloud3TargetX) {
                    // 智能清除：检查是否会影响时间方块，如果会则精确清除
                    extern Block hourBlock;
                    extern Block minuteBlock;

                    // 云朵旧位置区域
                    int16_t cloudOldX = cloud3X;
                    int16_t cloudOldY = 66;
                    int16_t cloudW = 26 * 3;
                    int16_t cloudH = 12 * 3;

                    // 检查是否与时间方块重叠
                    bool overlapHour = (hourBlock.getX() < cloudOldX + cloudW && hourBlock.getX() + hourBlock.getWidth() > cloudOldX &&
                                       hourBlock.getY() < cloudOldY + cloudH && hourBlock.getY() + hourBlock.getHeight() > cloudOldY);
                    bool overlapMinute = (minuteBlock.getX() < cloudOldX + cloudW && minuteBlock.getX() + minuteBlock.getWidth() > cloudOldX &&
                                         minuteBlock.getY() < cloudOldY + cloudH && minuteBlock.getY() + minuteBlock.getHeight() > cloudOldY);

                    if (overlapHour || overlapMinute) {
                        // 有重叠时，只清除云朵像素，保留时间方块像素
                        for (int j = 0; j < cloudH; j++) {
                            for (int i = 0; i < cloudW; i++) {
                                int16_t pixelX = cloudOldX + i;
                                int16_t pixelY = cloudOldY + j;

                                // 检查这个像素是否在时间方块区域内
                                bool inHourBlock = (overlapHour && pixelX >= hourBlock.getX() && pixelX < hourBlock.getX() + hourBlock.getWidth() &&
                                                   pixelY >= hourBlock.getY() && pixelY < hourBlock.getY() + hourBlock.getHeight());
                                bool inMinuteBlock = (overlapMinute && pixelX >= minuteBlock.getX() && pixelX < minuteBlock.getX() + minuteBlock.getWidth() &&
                                                     pixelY >= minuteBlock.getY() && pixelY < minuteBlock.getY() + minuteBlock.getHeight());

                                // 只清除不在时间方块内的像素
                                if (!inHourBlock && !inMinuteBlock) {
                                    updatePixelVirtual(pixelX, pixelY, SKY_COLOR);
                                }
                            }
                        }
                    } else {
                        // 无重叠时，正常清除整个区域
                        updateRectVirtual(cloudOldX, cloudOldY, cloudW, cloudH, SKY_COLOR);
                    }

                    // 向左移动 CLOUD3
                    cloud3X--;
                    needsRedraw = true;
                } else {
                    // 到达左侧，开始等待
                    cloudState = WAITING_LEFT;
                    waitStartTime = currentTime;
                }
                break;

            case WAITING_LEFT:
                if (currentTime - waitStartTime >= WAIT_DURATION) {
                    // 等待结束，开始向右移动，循环重新开始
                    cloudState = MOVING_RIGHT;
                    cloud3TargetX = CLOUD3_RIGHT_POS;
                }
                break;
        }

        // 上方CLOUD3 云朵连接动画逻辑
        switch (cloud3UpperState) {
            case MOVING_LEFT:
                if (cloud3UpperX > cloud3UpperTargetX) {
                    // 智能清除：检查是否会影响时间方块，如果会则精确清除
                    extern Block hourBlock;
                    extern Block minuteBlock;

                    // 上方云朵旧位置区域
                    int16_t cloudOldX = cloud3UpperX;
                    int16_t cloudOldY = 24;
                    int16_t cloudW = 26 * 3;
                    int16_t cloudH = 12 * 3;

                    // 检查是否与时间方块重叠
                    bool overlapHour = (hourBlock.getX() < cloudOldX + cloudW && hourBlock.getX() + hourBlock.getWidth() > cloudOldX &&
                                       hourBlock.getY() < cloudOldY + cloudH && hourBlock.getY() + hourBlock.getHeight() > cloudOldY);
                    bool overlapMinute = (minuteBlock.getX() < cloudOldX + cloudW && minuteBlock.getX() + minuteBlock.getWidth() > cloudOldX &&
                                         minuteBlock.getY() < cloudOldY + cloudH && minuteBlock.getY() + minuteBlock.getHeight() > cloudOldY);

                    if (overlapHour || overlapMinute) {
                        // 有重叠时，只清除云朵像素，保留时间方块像素
                        for (int j = 0; j < cloudH; j++) {
                            for (int i = 0; i < cloudW; i++) {
                                int16_t pixelX = cloudOldX + i;
                                int16_t pixelY = cloudOldY + j;

                                // 检查这个像素是否在时间方块区域内
                                bool inHourBlock = (overlapHour && pixelX >= hourBlock.getX() && pixelX < hourBlock.getX() + hourBlock.getWidth() &&
                                                   pixelY >= hourBlock.getY() && pixelY < hourBlock.getY() + hourBlock.getHeight());
                                bool inMinuteBlock = (overlapMinute && pixelX >= minuteBlock.getX() && pixelX < minuteBlock.getX() + minuteBlock.getWidth() &&
                                                     pixelY >= minuteBlock.getY() && pixelY < minuteBlock.getY() + minuteBlock.getHeight());

                                // 只清除不在时间方块内的像素
                                if (!inHourBlock && !inMinuteBlock) {
                                    updatePixelVirtual(pixelX, pixelY, SKY_COLOR);
                                }
                            }
                        }
                    } else {
                        // 无重叠时，正常清除整个区域
                        updateRectVirtual(cloudOldX, cloudOldY, cloudW, cloudH, SKY_COLOR);
                    }

                    // 向左移动 上方CLOUD3
                    cloud3UpperX--;
                    needsRedraw = true;
                } else {
                    // 到达左侧，开始等待
                    cloud3UpperState = WAITING_LEFT;
                    cloud3UpperWaitStartTime = currentTime;
                }
                break;

            case WAITING_LEFT:
                if (currentTime - cloud3UpperWaitStartTime >= WAIT_DURATION) {
                    // 等待结束，开始向右移动
                    cloud3UpperState = MOVING_RIGHT;
                    cloud3UpperTargetX = CLOUD3_UPPER_RIGHT_POS;
                }
                break;

            case MOVING_RIGHT:
                if (cloud3UpperX < cloud3UpperTargetX) {
                    // 智能清除：检查是否会影响时间方块，如果会则精确清除
                    extern Block hourBlock;
                    extern Block minuteBlock;

                    // 上方云朵旧位置区域
                    int16_t cloudOldX = cloud3UpperX;
                    int16_t cloudOldY = 24;
                    int16_t cloudW = 26 * 3;
                    int16_t cloudH = 12 * 3;

                    // 检查是否与时间方块重叠
                    bool overlapHour = (hourBlock.getX() < cloudOldX + cloudW && hourBlock.getX() + hourBlock.getWidth() > cloudOldX &&
                                       hourBlock.getY() < cloudOldY + cloudH && hourBlock.getY() + hourBlock.getHeight() > cloudOldY);
                    bool overlapMinute = (minuteBlock.getX() < cloudOldX + cloudW && minuteBlock.getX() + minuteBlock.getWidth() > cloudOldX &&
                                         minuteBlock.getY() < cloudOldY + cloudH && minuteBlock.getY() + minuteBlock.getHeight() > cloudOldY);

                    if (overlapHour || overlapMinute) {
                        // 有重叠时，只清除云朵像素，保留时间方块像素
                        for (int j = 0; j < cloudH; j++) {
                            for (int i = 0; i < cloudW; i++) {
                                int16_t pixelX = cloudOldX + i;
                                int16_t pixelY = cloudOldY + j;

                                // 检查这个像素是否在时间方块区域内
                                bool inHourBlock = (overlapHour && pixelX >= hourBlock.getX() && pixelX < hourBlock.getX() + hourBlock.getWidth() &&
                                                   pixelY >= hourBlock.getY() && pixelY < hourBlock.getY() + hourBlock.getHeight());
                                bool inMinuteBlock = (overlapMinute && pixelX >= minuteBlock.getX() && pixelX < minuteBlock.getX() + minuteBlock.getWidth() &&
                                                     pixelY >= minuteBlock.getY() && pixelY < minuteBlock.getY() + minuteBlock.getHeight());

                                // 只清除不在时间方块内的像素
                                if (!inHourBlock && !inMinuteBlock) {
                                    updatePixelVirtual(pixelX, pixelY, SKY_COLOR);
                                }
                            }
                        }
                    } else {
                        // 无重叠时，正常清除整个区域
                        updateRectVirtual(cloudOldX, cloudOldY, cloudW, cloudH, SKY_COLOR);
                    }

                    // 向右移动 上方CLOUD3
                    cloud3UpperX++;
                    needsRedraw = true;
                } else {
                    // 到达右侧，开始等待
                    cloud3UpperState = WAITING_RIGHT;
                    cloud3UpperWaitStartTime = currentTime;
                }
                break;

            case WAITING_RIGHT:
                if (currentTime - cloud3UpperWaitStartTime >= WAIT_DURATION) {
                    // 等待结束，开始向左移动，循环重新开始
                    cloud3UpperState = MOVING_LEFT;
                    cloud3UpperTargetX = CLOUD3_UPPER_LEFT_POS;
                }
                break;
        }

        // 如果需要重绘（即任一云朵移动了）
        if (needsRedraw) {
            // 绘制移动后的完整 CLOUD3（下方云朵，Y位置降低3px）
            // CLOUD3 原始尺寸 26x12
            for (int j = 0; j < 12 * 3; j++) {  // 高度 36 (12 * 3)
                for (int i = 0; i < 26 * 3; i++) {  // 宽度 78 (26 * 3)
                    int src_j = j / 3; // 源数据的行索引 (0-11)
                    int src_i = i / 3; // 源数据的列索引 (0-25)
                    uint16_t pixel = CLOUD3[src_j * 26 + src_i];
                    if (pixel != SKY_COLOR) {  // 跳过背景色
                        updatePixelVirtual(cloud3X + i, 66 + j, pixel); // 从63改为66，降低3px
                    }
                }
            }

            // 绘制移动后的上方CLOUD3（完整循环动画，Y位置降低3px）
            // CLOUD3 原始尺寸 26x12
            for (int j = 0; j < 12 * 3; j++) {  // 高度 36
                for (int i = 0; i < 26 * 3; i++) {  // 宽度 78
                    int src_j = j / 3;
                    int src_i = i / 3;
                    uint16_t pixel = CLOUD3[src_j * 26 + src_i]; // 使用 CLOUD3 数据
                    if (pixel != SKY_COLOR) {  // 跳过背景色
                        updatePixelVirtual(cloud3UpperX + i, 24 + j, pixel); // 从21改为24，降低3px
                    }
                }
            }


            // 获取外部对象引用，确保时间方块和马里奥始终在最上层
            extern Block hourBlock;
            extern Block minuteBlock;
            extern Mario mario;

            // 检查并重绘可能被移动的 CLOUD3 覆盖的元素
            // CLOUD3 的包围盒是 (cloud3X, 66, 26*3, 12*3) - Y位置降低3px
            int16_t cloud3W = 26 * 3;
            int16_t cloud3H = 12 * 3;
            int16_t cloud3Y = 66;

            if (hourBlock.getX() < cloud3X + cloud3W && hourBlock.getX() + hourBlock.getWidth() > cloud3X &&
                hourBlock.getY() < cloud3Y + cloud3H && hourBlock.getY() + hourBlock.getHeight() > cloud3Y) {
                hourBlock.draw();
            }
            if (minuteBlock.getX() < cloud3X + cloud3W && minuteBlock.getX() + minuteBlock.getWidth() > cloud3X &&
                minuteBlock.getY() < cloud3Y + cloud3H && minuteBlock.getY() + minuteBlock.getHeight() > cloud3Y) {
                minuteBlock.draw();
            }
            if (mario.getX() < cloud3X + cloud3W && mario.getX() + mario.getWidth() > cloud3X &&
                mario.getY() < cloud3Y + cloud3H && mario.getY() + mario.getHeight() > cloud3Y) {
                mario.draw();
            }

            // 上方CLOUD3的覆盖检测 (cloud3UpperX, 24, 26*3, 12*3) - Y位置降低3px
            int16_t cloud3UpperW = 26 * 3;
            int16_t cloud3UpperH = 12 * 3;
            int16_t cloud3UpperY = 24;
             if (hourBlock.getX() < cloud3UpperX + cloud3UpperW && hourBlock.getX() + hourBlock.getWidth() > cloud3UpperX &&
                hourBlock.getY() < cloud3UpperY + cloud3UpperH && hourBlock.getY() + hourBlock.getHeight() > cloud3UpperY) {
                hourBlock.draw();
            }
            if (minuteBlock.getX() < cloud3UpperX + cloud3UpperW && minuteBlock.getX() + minuteBlock.getWidth() > cloud3UpperX &&
                minuteBlock.getY() < cloud3UpperY + cloud3UpperH && minuteBlock.getY() + minuteBlock.getHeight() > cloud3UpperY) {
                minuteBlock.draw();
            }
             if (mario.getX() < cloud3UpperX + cloud3UpperW && mario.getX() + mario.getWidth() > cloud3UpperX &&
                mario.getY() < cloud3UpperY + cloud3UpperH && mario.getY() + mario.getHeight() > cloud3UpperY) {
                mario.draw();
            }


            // 如果使用双缓冲，渲染到屏幕
            if (useFrameBuffer) {
                renderFrameBuffer();
            }
        }
        lastUpdate = currentTime;
    }
}

// 新增：绘制云朵立方体精灵的函数
void drawCloudCubeSprite(int16_t x, int16_t y) {
    // 获取外部定义的gfx对象
    extern Arduino_GFX *gfx;

    // 云朵立方体精灵尺寸（16x16像素，不需要缩放）
    int16_t cubeWidth = CUBE_CLOUD_SIZE[0];   // 16像素
    int16_t cubeHeight = CUBE_CLOUD_SIZE[1];  // 16像素

    // 绘制云朵立方体精灵（16x16像素，直接绘制到屏幕，不需要缩放）
    for (int j = 0; j < cubeHeight; j++) {
        for (int i = 0; i < cubeWidth; i++) {
            uint16_t pixel = CUBE_CLOUD[j * cubeWidth + i];
            if (pixel != _MASK_GOLDEN) {  // 跳过透明像素
                gfx->drawPixel(x + i, y + j, pixel);
            }
        }
    }
}

// 新增：绘制管道精灵的函数
void drawPipeSprite(int16_t x, int16_t y) {
    // 获取外部定义的gfx对象
    extern Arduino_GFX *gfx;

    // 管道精灵尺寸（20x20像素，不需要缩放）
    int16_t pipeWidth = PIPE_SIZE[0];   // 20像素
    int16_t pipeHeight = PIPE_SIZE[1];  // 20像素

    // 绘制管道精灵（20x20像素，直接绘制到屏幕，不需要缩放）
    for (int j = 0; j < pipeHeight; j++) {
        for (int i = 0; i < pipeWidth; i++) {
            uint16_t pixel = pipeshuiguan20[j * pipeWidth + i];
            if (pixel != 0x5cbf) {  // 跳过透明像素（使用管道精灵的透明色）
                gfx->drawPixel(x + i, y + j, pixel);
            }
        }
    }
}

// 新增：绘制角落方形精灵的函数
void drawCornerSquares() {
    // 获取外部定义的偏移量和尺寸
    extern const int OFFSET_X;
    extern const int OFFSET_Y;
    extern const int VIRTUAL_WIDTH;
    extern const int VIRTUAL_HEIGHT;
    extern Arduino_GFX *gfx;

    // 复古金色区域的边界（240×240屏幕，无黑色区域）
    const int16_t GOLDEN_AREA_TOP = 0;       // 屏幕顶部
    const int16_t GOLDEN_AREA_BOTTOM = 240;  // 屏幕底部
    const int16_t GOLDEN_AREA_LEFT = 0;      // 屏幕左边缘
    const int16_t GOLDEN_AREA_RIGHT = 240;   // 屏幕右边缘

    // 20x20像素方形精灵的尺寸（不需要缩放）
    int16_t squareWidth = SQUARE_SIZE[0];   // 20像素
    int16_t squareHeight = SQUARE_SIZE[1];  // 20像素

    // 计算四个角的位置（严格按照复古金色区域边界）
    // 左上角：复古金色区域的左上角
    int16_t topLeftX = GOLDEN_AREA_LEFT;
    int16_t topLeftY = GOLDEN_AREA_TOP;

    // 右上角：复古金色区域的右上角
    int16_t topRightX = GOLDEN_AREA_RIGHT - squareWidth;
    int16_t topRightY = GOLDEN_AREA_TOP;

    // 左下角：复古金色区域的左下角
    int16_t bottomLeftX = GOLDEN_AREA_LEFT;
    int16_t bottomLeftY = GOLDEN_AREA_BOTTOM - squareHeight;

    // 右下角：复古金色区域的右下角
    int16_t bottomRightX = GOLDEN_AREA_RIGHT - squareWidth;
    int16_t bottomRightY = GOLDEN_AREA_BOTTOM - squareHeight;

    // 绘制四个角的方形精灵（20x20像素，直接绘制到屏幕，不需要缩放）
    // 左上角
    for (int j = 0; j < squareHeight; j++) {
        for (int i = 0; i < squareWidth; i++) {
            uint16_t pixel = SQUARE[j * squareWidth + i];
            if (pixel != _MASK_GOLDEN) {  // 跳过透明像素
                gfx->drawPixel(topLeftX + i, topLeftY + j, pixel);
            }
        }
    }

    // 右上角
    for (int j = 0; j < squareHeight; j++) {
        for (int i = 0; i < squareWidth; i++) {
            uint16_t pixel = SQUARE[j * squareWidth + i];
            if (pixel != _MASK_GOLDEN) {  // 跳过透明像素
                gfx->drawPixel(topRightX + i, topRightY + j, pixel);
            }
        }
    }

    // 左下角
    for (int j = 0; j < squareHeight; j++) {
        for (int i = 0; i < squareWidth; i++) {
            uint16_t pixel = SQUARE[j * squareWidth + i];
            if (pixel != _MASK_GOLDEN) {  // 跳过透明像素
                gfx->drawPixel(bottomLeftX + i, bottomLeftY + j, pixel);
            }
        }
    }

    // 右下角
    for (int j = 0; j < squareHeight; j++) {
        for (int i = 0; i < squareWidth; i++) {
            uint16_t pixel = SQUARE[j * squareWidth + i];
            if (pixel != _MASK_GOLDEN) {  // 跳过透明像素
                gfx->drawPixel(bottomRightX + i, bottomRightY + j, pixel);
            }
        }
    }

    // WiFi精灵 - 放置在最右上角方形精灵的左边（当WiFi AP开启时显示）
    int16_t wifiX = topRightX - squareWidth;  // 最右上角方形精灵左边
    int16_t wifiY = topRightY;                // 与最右上角方形精灵同一水平线

    // 检测WiFi AP是否开启
    bool hasWiFiActivity = wifiMgr.isAPModeActive();

    if (hasWiFiActivity) {
        // 有WiFi活动时，绘制WiFi精灵
        for (int j = 0; j < squareHeight; j++) {
            for (int i = 0; i < squareWidth; i++) {
                uint16_t pixel = WIFI_LOGO[j * squareWidth + i];
                if (pixel != _MASK_GOLDEN) {  // 跳过透明像素
                    gfx->drawPixel(wifiX + i, wifiY + j, pixel);
                }
            }
        }
    } else {
        // 没有WiFi活动时，清除WiFi精灵区域（填充为复古金色背景）
        gfx->fillRect(wifiX, wifiY, squareWidth, squareHeight, _MASK_GOLDEN);
    }

    // 简化版本：马里奥时钟模式，清除所有云朵立方体精灵区域并绘制树藤精灵
    int16_t cubeWidth = CUBE_CLOUD_SIZE[0];   // 16像素
    int16_t cubeHeight = CUBE_CLOUD_SIZE[1];  // 16像素
    int16_t cubeSpacing = 2;  // 云朵之间的间距

    int16_t startX = topLeftX + squareWidth;   // 最左上角方形精灵右侧
    int16_t startY = topLeftY + (squareHeight - cubeHeight) / 2;  // 上下居中

    // 清除最大可能的云朵区域（4个云朵的总宽度）
    int16_t maxCloudsWidth = 4 * cubeWidth + 3 * cubeSpacing;
    gfx->fillRect(startX, startY, maxCloudsWidth, cubeHeight, _MASK_GOLDEN);

    // 马里奥时钟模式下，在左下角方块精灵上方绘制管道精灵
    // 计算管道精灵的位置：底部贴合左下角方块精灵顶部，左右居中
    int16_t pipeWidth = PIPE_SIZE[0];   // 20像素
    int16_t pipeHeight = PIPE_SIZE[1];  // 20像素

    // 左下角管道精灵X坐标：左下角方块精灵左右居中
    int16_t leftPipeX = bottomLeftX + (squareWidth - pipeWidth) / 2;  // (20-20)/2 = 0像素偏移
    // 左下角管道精灵Y坐标：底部贴合左下角方块精灵顶部
    int16_t leftPipeY = bottomLeftY - pipeHeight;  // 底部贴合方块精灵顶部

    // 绘制左下角管道精灵
    drawPipeSprite(leftPipeX, leftPipeY);

    // 在右下角方块精灵上方绘制管道精灵
    // 右下角管道精灵X坐标：右下角方块精灵左右居中
    int16_t rightPipeX = bottomRightX + (squareWidth - pipeWidth) / 2;  // (20-20)/2 = 0像素偏移
    // 右下角管道精灵Y坐标：底部贴合右下角方块精灵顶部
    int16_t rightPipeY = bottomRightY - pipeHeight;  // 底部贴合方块精灵顶部

    // 绘制右下角管道精灵
    drawPipeSprite(rightPipeX, rightPipeY);
}

void Scene::init() {
    // 初始化帧缓冲区
    if (!initFrameBuffer()) {
        // 如果无法分配内存，则禁用帧缓冲
        useFrameBuffer = false;
    }

    draw();

    // 移除所有砖墙，恢复原来纯复古金色的外围区域
    // 现在外围区域将显示纯净的FC_GOLDEN复古金色背景

    // 如果使用双缓冲，渲染到屏幕
    if (useFrameBuffer) {
        renderFrameBuffer();
    }
}

void Scene::draw() {
    // 绘制天空
    updateRectVirtual(0, 0, VIRTUAL_WIDTH, VIRTUAL_HEIGHT, SKY_COLOR);

    // 绘制云和山
    drawClouds();
    drawHills();
    drawBushes();

    // 绘制地面
    drawGround();
}

// 修改Scene::drawGroundTile()函数
void Scene::drawGroundTile(int16_t x, int16_t y) {
    // 绘制一个24x24的地面瓦片(3倍缩放)
    for (int row = 0; row < 24; row++) {
        for (int col = 0; col < 24; col++) {
            // 映射到原始8×8瓦片
            int src_row = row / 3;
            int src_col = col / 3;
            uint16_t color = GROUND_TILE[src_row * 8 + src_col];
            if (color != 0) {  // 忽略透明像素
                updatePixelVirtual(x + col, y + row, color);
            }
        }
    }
}

// 修改Scene::drawGround()函数
void Scene::drawGround() {
    // 使用地面瓦片填充底部区域
    for (int x = 0; x < VIRTUAL_WIDTH; x += 24) { // 原来是x += 8
        drawGroundTile(x, VIRTUAL_HEIGHT - GROUND_HEIGHT);
    }
}

// 绘制13x12像素的云朵 - 3倍缩放
void drawCloudSprite(int16_t x, int16_t y, const uint16_t* cloudData) {
    for (int j = 0; j < 36; j++) {  // 12 * 3
        for (int i = 0; i < 39; i++) {  // 13 * 3
            int src_j = j / 3;
            int src_i = i / 3;
            uint16_t pixel = cloudData[src_j * 13 + src_i];
            if (pixel != SKY_COLOR) {  // 跳过背景色
                updatePixelVirtual(x + i, y + j, pixel);
            }
        }
    }
}

void Scene::drawClouds() {
    // 绘制完整的 CLOUD3 精灵在其当前 X 位置（Y位置降低3px）
    // CLOUD3 原始尺寸 26x12
    for (int j = 0; j < 12 * 3; j++) {  // 高度 36 (12 * 3)
        for (int i = 0; i < 26 * 3; i++) {  // 宽度 78 (26 * 3)
            int src_j = j / 3; // 源数据的行索引 (0-11)
            int src_i = i / 3; // 源数据的列索引 (0-25)
            uint16_t pixel = CLOUD3[src_j * 26 + src_i];
            if (pixel != SKY_COLOR) {  // 跳过背景色
                updatePixelVirtual(cloud3X + i, 66 + j, pixel); // 从63改为66，降低3px
            }
        }
    }

    // 绘制上方的动画CLOUD3（完整循环动画，Y位置降低3px）
    // CLOUD3 原始尺寸 26x12
    for (int j = 0; j < 12 * 3; j++) {  // 高度 36
        for (int i = 0; i < 26 * 3; i++) {  // 宽度 78
            int src_j = j / 3;
            int src_i = i / 3;
            uint16_t pixel = CLOUD3[src_j * 26 + src_i]; // 使用 CLOUD3 数据
            if (pixel != SKY_COLOR) {  // 跳过背景色
                updatePixelVirtual(cloud3UpperX + i, 24 + j, pixel); // 从21改为24，降低3px
            }
        }
    }
}

// 绘制20x22像素的山丘 - 3倍缩放
void drawHillSprite(int16_t x, int16_t y, const uint16_t* hillData) {
    for (int j = 0; j < 66; j++) {  // 22 * 3
        for (int i = 0; i < 60; i++) {  // 20 * 3
            int src_j = j / 3;
            int src_i = i / 3;
            uint16_t pixel = hillData[src_j * 20 + src_i];
            if (pixel != SKY_COLOR) {  // 跳过背景色
                updatePixelVirtual(x + i, y + j, pixel);
            }
        }
    }
}

void Scene::drawHills() {
    // 绘制山丘，与原版位置保持一致，但位置乘以3
    drawHillSprite(0, 102, HILL);  // 山丘位置 - 原坐标(0, 34)*3
}

// 绘制21x9像素的树丛 - 3倍缩放
void drawBushSprite(int16_t x, int16_t y, const uint16_t* bushData) {
    for (int j = 0; j < 27; j++) {  // 9 * 3
        for (int i = 0; i < 63; i++) {  // 21 * 3
            int src_j = j / 3;
            int src_i = i / 3;
            uint16_t pixel = bushData[src_j * 21 + src_i];
            if (pixel != SKY_COLOR) {  // 跳过背景色
                updatePixelVirtual(x + i, y + j, pixel);
            }
        }
    }
}

void Scene::drawBushes() {
    // 绘制树丛，与原版位置保持一致，但位置乘以3
    drawBushSprite(129, 141, BUSH);  // 树丛位置 - 原坐标(43, 47)*3
}

// 新增：重绘指定区域内的云朵部分
void Scene::redrawCloudsInRegion(int16_t x, int16_t y, int16_t w, int16_t h) {
    // 检查CLOUD3是否与指定区域重叠
    int16_t cloud3W = 26 * 3;  // CLOUD3宽度：78
    int16_t cloud3H = 12 * 3;  // CLOUD3高度：36
    int16_t cloud3Y = 66;      // CLOUD3的Y位置（降低3px）

    if (cloud3X < x + w && cloud3X + cloud3W > x &&
        cloud3Y < y + h && cloud3Y + cloud3H > y) {
        // CLOUD3与指定区域重叠，重绘重叠部分
        for (int j = 0; j < cloud3H; j++) {
            for (int i = 0; i < cloud3W; i++) {
                int16_t pixelX = cloud3X + i;
                int16_t pixelY = cloud3Y + j;

                // 只重绘在指定区域内的像素
                if (pixelX >= x && pixelX < x + w &&
                    pixelY >= y && pixelY < y + h) {
                    int src_j = j / 3;
                    int src_i = i / 3;
                    uint16_t pixel = CLOUD3[src_j * 26 + src_i];
                    if (pixel != SKY_COLOR) {
                        updatePixelVirtual(pixelX, pixelY, pixel);
                    }
                }
            }
        }
    }

    // 检查上方动画CLOUD3是否与指定区域重叠
    int16_t cloud3UpperW = 26 * 3;  // CLOUD3宽度：78
    int16_t cloud3UpperH = 12 * 3;  // CLOUD3高度：36
    int16_t cloud3UpperY = 24;      // 上方CLOUD3的Y位置（降低3px）

    if (cloud3UpperX < x + w && cloud3UpperX + cloud3UpperW > x &&
        cloud3UpperY < y + h && cloud3UpperY + cloud3UpperH > y) {
        // 上方动画CLOUD3与指定区域重叠，重绘重叠部分
        for (int j = 0; j < cloud3UpperH; j++) {
            for (int i = 0; i < cloud3UpperW; i++) {
                int16_t pixelX = cloud3UpperX + i;
                int16_t pixelY = cloud3UpperY + j;

                // 只重绘在指定区域内的像素
                if (pixelX >= x && pixelX < x + w &&
                    pixelY >= y && pixelY < y + h) {
                    int src_j = j / 3;
                    int src_i = i / 3;
                    uint16_t pixel = CLOUD3[src_j * 26 + src_i];
                    if (pixel != SKY_COLOR) {
                        updatePixelVirtual(pixelX, pixelY, pixel);
                    }
                }
            }
        }
    }
}





// 新增：性能优化方法实现
void Scene::pauseCloudAnimation() {
    cloudAnimationPaused = true;
}

void Scene::resumeCloudAnimation() {
    cloudAnimationPaused = false;
    // 恢复时重置时间，避免时间跳跃
    lastUpdate = millis();
}

bool Scene::isCloudAnimationPaused() const {
    return cloudAnimationPaused;
}

// Block类的番茄钟相关方法实现（简化版本）
bool Block::isPomodoroMode() {
    return false;  // 简化版本：总是返回false，只使用马里奥时钟模式
}

void Block::displayPomodoroTime() {
    // 简化版本：不使用番茄钟，此方法为空
    // 保留方法以保持接口兼容性
}

void Block::displayCurrentTime() {
    // 原有的HH:MM显示逻辑
    // 获取外部时间变量
    extern int currentHour;
    extern int currentMinute;

    // 根据方块位置决定显示内容
    extern Block hourBlock;
    extern Block minuteBlock;

    if (this == &hourBlock) {
        // 小时方块显示小时
        char hourStr[3];
        sprintf(hourStr, "%d", currentHour);
        setText(String(hourStr));
    } else if (this == &minuteBlock) {
        // 分钟方块显示分钟
        char minuteStr[3];
        sprintf(minuteStr, "%02d", currentMinute);
        setText(String(minuteStr));
    }
}

// 颜色控制方法实现（简化版本）
uint16_t Block::getCurrentColor() {
    // 简化版本：总是返回默认的方块颜色
    return POMODORO_WORK_COLOR;
}

uint16_t Block::getBlinkingColor() {
    // 注意：此函数已不再使用，暂停状态不再闪烁
    // 保留此函数以备将来可能的需要
    // 简单的闪烁效果：每500ms切换一次透明度
    static unsigned long lastBlink = 0;
    static bool isVisible = true;

    if (millis() - lastBlink > 500) {
        isVisible = !isVisible;
        lastBlink = millis();
    }

    return isVisible ? POMODORO_WORK_COLOR : 0x0000;  // 黑色表示暗
}

void Block::drawBlockWithColor(uint16_t blockColor) {
    // 绘制带指定颜色的问号方块
    for (int i = 0; i < BLOCK_HEIGHT; i++) {
        for (int j = 0; j < BLOCK_WIDTH; j++) {
            int i_src = i * 19 / BLOCK_HEIGHT; // 转换为源数据索引
            int j_src = j * 19 / BLOCK_WIDTH; // 转换为源数据索引
            uint16_t pixel = BLOCK[i_src * 19 + j_src];
            if (pixel != SKY_COLOR) {  // 忽略透明像素
                // 如果原始像素是方块颜色（0xE4E4），则替换为指定颜色
                if (pixel == 0xE4E4) {
                    updatePixelVirtual(x + j, y + i, blockColor);
                } else {
                    // 保持其他颜色不变（如边框颜色等）
                    updatePixelVirtual(x + j, y + i, pixel);
                }
            }
        }
    }
}

// ==================== 透明图层机制增强 ====================

// 通用透明位图绘制函数（参考原版项目的透明处理机制）
void drawTransparentBitmap(int16_t x, int16_t y, const uint16_t* bitmap, int16_t w, int16_t h, int scale = 1) {
    for (int j = 0; j < h; j++) {
        for (int i = 0; i < w; i++) {
            uint16_t pixel = bitmap[j * w + i];
            if (pixel != _MASK) {  // 只绘制非透明像素
                // 根据缩放比例绘制像素块
                for (int sy = 0; sy < scale; sy++) {
                    for (int sx = 0; sx < scale; sx++) {
                        int16_t drawX = x + (i * scale) + sx;
                        int16_t drawY = y + (j * scale) + sy;

                        if (useFrameBuffer) {
                            updatePixelVirtual(drawX, drawY, pixel);
                        } else {
                            drawPixelVirtual(drawX, drawY, pixel);
                        }
                    }
                }
            }
        }
    }
}

// 通用透明位图绘制函数（直接绘制到屏幕，用于边框区域）
void drawTransparentBitmapDirect(int16_t x, int16_t y, const uint16_t* bitmap, int16_t w, int16_t h, uint16_t maskColor = _MASK_GOLDEN) {
    for (int j = 0; j < h; j++) {
        for (int i = 0; i < w; i++) {
            uint16_t pixel = bitmap[j * w + i];
            if (pixel != maskColor) {  // 只绘制非透明像素
                gfx->drawPixel(x + i, y + j, pixel);
            }
        }
    }
}

// 图层冲突检测函数（参考原版项目的像素冲突检测机制）
bool isPixelInsideBlocks(int16_t x, int16_t y) {
    // 检查像素是否在时间方块内
    extern Block hourBlock;
    extern Block minuteBlock;

    // 检查小时方块
    if (x >= hourBlock.getX() && x < hourBlock.getX() + hourBlock.getWidth() &&
        y >= hourBlock.getY() && y < hourBlock.getY() + hourBlock.getHeight()) {
        return true;
    }

    // 检查分钟方块
    if (x >= minuteBlock.getX() && x < minuteBlock.getX() + minuteBlock.getWidth() &&
        y >= minuteBlock.getY() && y < minuteBlock.getY() + minuteBlock.getHeight()) {
        return true;
    }

    return false;
}

// 增强的透明位图绘制函数（带图层冲突检测）
void drawTransparentBitmapWithLayerCheck(int16_t x, int16_t y, const uint16_t* bitmap, int16_t w, int16_t h, int scale = 1, LayerPriority layer = LAYER_SPRITES) {
    for (int j = 0; j < h; j++) {
        for (int i = 0; i < w; i++) {
            uint16_t pixel = bitmap[j * w + i];
            if (pixel != _MASK) {  // 只绘制非透明像素
                // 根据缩放比例绘制像素块
                for (int sy = 0; sy < scale; sy++) {
                    for (int sx = 0; sx < scale; sx++) {
                        int16_t drawX = x + (i * scale) + sx;
                        int16_t drawY = y + (j * scale) + sy;

                        // 图层冲突检测：精灵层不应覆盖方块层
                        if (layer == LAYER_SPRITES && isPixelInsideBlocks(drawX, drawY)) {
                            continue;  // 跳过与方块冲突的像素
                        }

                        if (useFrameBuffer) {
                            updatePixelVirtual(drawX, drawY, pixel);
                        } else {
                            drawPixelVirtual(drawX, drawY, pixel);
                        }
                    }
                }
            }
        }
    }
}

// ==================== 栗子精灵类实现 ====================

Goomba::Goomba(int16_t x, int16_t y) {
    this->x = x;
    this->y = y;
    this->startX = x;  // 记录起始位置
    this->targetX = x + MOVEMENT_DISTANCE;  // 目标位置
    this->direction = RIGHT;  // 初始向右移动

    this->spriteState = LEFT_SPRITE;
    this->movementState = IDLE_AT_START;
    this->currentSprite = lizizuo;  // 初始使用左精灵

    this->lastSpriteChange = 0;
    this->lastMovementUpdate = 0;
    this->idleStartTime = 0;
}

void Goomba::init() {
    // 调试输出栗子精灵位置
    // Serial.print("Goomba: Initializing at position (");
    // Serial.print(x);
    // Serial.print(", ");
    // Serial.print(y);
    // Serial.print(") with size ");
    // Serial.print(getWidth());
    // Serial.print("x");
    // Serial.print(getHeight());
    // Serial.print(", movement range: ");
    // Serial.print(startX);
    // Serial.print(" to ");
    // Serial.println(targetX);

    // 初始化时间
    unsigned long currentTime = millis();
    lastSpriteChange = currentTime;
    lastMovementUpdate = currentTime;
    idleStartTime = currentTime;

    // 绘制初始状态的栗子精灵 - 3倍缩放，使用左精灵
    drawScaledSprite(x, y, currentSprite);
}

void Goomba::update() {
    unsigned long currentTime = millis();

    // 处理精灵动画切换（独立于移动）
    if (currentTime - lastSpriteChange >= SPRITE_ANIMATION_INTERVAL) {
        // 切换精灵状态
        if (spriteState == LEFT_SPRITE) {
            spriteState = RIGHT_SPRITE;
            currentSprite = liziyou;
        } else {
            spriteState = LEFT_SPRITE;
            currentSprite = lizizuo;
        }
        lastSpriteChange = currentTime;
    }

    // 处理移动逻辑（参考马里奥跳跃机制）
    if (currentTime - lastMovementUpdate >= MOVEMENT_UPDATE_INTERVAL) {
        // 保存旧位置
        int16_t oldX = x;
        int16_t oldY = y;
        bool needsRedraw = false;

        switch (movementState) {
            case IDLE_AT_START:
                if (currentTime - idleStartTime >= IDLE_DURATION) {
                    movementState = MOVING_RIGHT;
                    direction = RIGHT;
                    targetX = startX + MOVEMENT_DISTANCE;
                    // Serial.println("Goomba: Starting move right");
                }
                break;

            case MOVING_RIGHT:
                if (x < targetX) {
                    x += MOVEMENT_PACE;
                    needsRedraw = true;
                } else {
                    movementState = IDLE_AT_END;
                    idleStartTime = currentTime;
                    // Serial.println("Goomba: Reached right, idling...");
                }
                break;

            case IDLE_AT_END:
                if (currentTime - idleStartTime >= IDLE_DURATION) {
                    movementState = MOVING_LEFT;
                    direction = LEFT;
                    targetX = startX;
                    // Serial.println("Goomba: Starting move left");
                }
                break;

            case MOVING_LEFT:
                if (x > targetX) {
                    x -= MOVEMENT_PACE;
                    needsRedraw = true;
                } else {
                    movementState = IDLE_AT_START;
                    idleStartTime = currentTime;
                    // Serial.println("Goomba: Reached start, idling...");
                }
                break;
        }

        // 如果位置发生变化，重绘（与原版项目完全一致的逻辑）
        if (needsRedraw) {
            // 重绘背景区域（山脉+天空+地砖）
            redrawBackground(oldX, oldY, getWidth(), getHeight());

            // 绘制新位置的精灵（使用当前精灵状态）
            drawScaledSprite(x, y, currentSprite);
        }

        lastMovementUpdate = currentTime;
    }
}

void Goomba::draw() {
    // 绘制栗子精灵 - 3倍缩放，使用当前精灵
    drawScaledSprite(x, y, currentSprite);
}

void Goomba::drawScaledSprite(int16_t x, int16_t y, const uint16_t* sprite) {
    // 使用带图层检测的透明位图绘制函数，3倍缩放，精灵层优先级
    drawTransparentBitmapWithLayerCheck(x, y, sprite, GOOMBA_SIZE[0], GOOMBA_SIZE[1], 3, LAYER_SPRITES);
}

void Goomba::redrawBackground(int16_t x, int16_t y, int16_t w, int16_t h) {
    // 先用天空色填充整个区域
    if (useFrameBuffer) {
        updateRectVirtual(x, y, w, h, SKY_COLOR);
    } else {
        fillRectVirtual(x, y, w, h, SKY_COLOR);
    }

    // 重绘山脉部分（山脉位置：X=0, Y=102, 宽度=60, 高度=66，即原版X=0, Y=34*3, 宽度=20*3, 高度=22*3）
    int16_t hillX = 0, hillY = 102, hillW = 60, hillH = 66;
    if (x < hillX + hillW && x + w > hillX && y < hillY + hillH && y + h > hillY) {
        // 计算重叠区域
        int16_t overlapX = (x > hillX) ? x : hillX;
        int16_t overlapY = (y > hillY) ? y : hillY;
        int16_t overlapW = ((x + w < hillX + hillW) ? (x + w) : (hillX + hillW)) - overlapX;
        int16_t overlapH = ((y + h < hillY + hillH) ? (y + h) : (hillY + hillH)) - overlapY;

        // 重绘山脉的重叠部分（3倍缩放）
        for (int j = 0; j < overlapH; j++) {
            for (int i = 0; i < overlapW; i++) {
                int16_t hillPixelX = (overlapX + i - hillX) / 3; // 转换为原始坐标系
                int16_t hillPixelY = (overlapY + j - hillY) / 3;
                if (hillPixelX >= 0 && hillPixelX < 20 && hillPixelY >= 0 && hillPixelY < 22) {
                    uint16_t pixel = HILL[hillPixelY * 20 + hillPixelX];
                    if (useFrameBuffer) {
                        updatePixelVirtual(overlapX + i, overlapY + j, pixel);
                    } else {
                        drawPixelVirtual(overlapX + i, overlapY + j, pixel);
                    }
                }
            }
        }
    }

    // 重绘树丛部分（树丛位置：X=129, Y=141, 宽度=63, 高度=27，即原版X=43*3, Y=47*3, 宽度=21*3, 高度=9*3）
    int16_t bushX = 129, bushY = 141, bushW = 63, bushH = 27;
    if (x < bushX + bushW && x + w > bushX && y < bushY + bushH && y + h > bushY) {
        // 计算重叠区域
        int16_t overlapX = (x > bushX) ? x : bushX;
        int16_t overlapY = (y > bushY) ? y : bushY;
        int16_t overlapW = ((x + w < bushX + bushW) ? (x + w) : (bushX + bushW)) - overlapX;
        int16_t overlapH = ((y + h < bushY + bushH) ? (y + h) : (bushY + bushH)) - overlapY;

        // 重绘树丛的重叠部分（3倍缩放）
        for (int j = 0; j < overlapH; j++) {
            for (int i = 0; i < overlapW; i++) {
                int16_t bushPixelX = (overlapX + i - bushX) / 3; // 转换为原始坐标系
                int16_t bushPixelY = (overlapY + j - bushY) / 3;
                if (bushPixelX >= 0 && bushPixelX < 21 && bushPixelY >= 0 && bushPixelY < 9) {
                    uint16_t pixel = BUSH[bushPixelY * 21 + bushPixelX];
                    if (pixel != _MASK) {  // 树丛也有透明像素
                        if (useFrameBuffer) {
                            updatePixelVirtual(overlapX + i, overlapY + j, pixel);
                        } else {
                            drawPixelVirtual(overlapX + i, overlapY + j, pixel);
                        }
                    }
                }
            }
        }
    }

    // 重绘地面部分（地面位置：Y=168, 高度=24，即原版Y=56*3, 高度=8*3）
    int16_t groundY = 168, groundH = 24;
    if (y < groundY + groundH && y + h > groundY) {
        // 计算重叠区域
        int16_t overlapY = (y > groundY) ? y : groundY;
        int16_t overlapH = ((y + h < groundY + groundH) ? (y + h) : (groundY + groundH)) - overlapY;

        // 重绘地面的重叠部分（使用3倍缩放的地面瓦片）
        for (int j = 0; j < overlapH; j++) {
            for (int i = 0; i < w; i++) {
                // 地面瓦片是8x8重复的，缩放后是24x24
                int16_t groundPixelX = ((x + i) / 3) % 8; // 转换为原始坐标系
                int16_t groundPixelY = ((overlapY + j - groundY) / 3) % 8;

                if (groundPixelY >= 0 && groundPixelY < 8 && groundPixelX >= 0 && groundPixelX < 8) {
                    uint16_t pixel = GROUND_TILE[groundPixelY * 8 + groundPixelX];
                    if (useFrameBuffer) {
                        updatePixelVirtual(x + i, overlapY + j, pixel);
                    } else {
                        drawPixelVirtual(x + i, overlapY + j, pixel);
                    }
                }
            }
        }
    }

    // 重绘云朵动画（参考原版项目的云朵重绘机制）
    // 获取场景引用以便重绘云朵
    extern Scene scene;
    scene.redrawCloudsInRegion(x, y, w, h);
}

// ==================== 动画精灵类实现（蘑菇和刺猬交替出现） ====================

AnimatedSprite::AnimatedSprite(int16_t x, int16_t y) {
    this->startX = x;  // 记录起始位置
    this->targetX = x + MOVEMENT_DISTANCE;  // 目标位置
    this->offScreenX = 192 + EXIT_DISTANCE;  // 屏幕外位置（192是当前屏幕宽度）
    this->x = offScreenX;  // 初始位置在屏幕外
    this->y = y;

    this->currentSpriteType = MUSHROOM_SPRITE;
    this->movementState = ENTERING_SCREEN;
    this->currentSprite = mogu;  // 初始使用蘑菇精灵

    this->lastSpriteChange = 0;
    this->lastMovementUpdate = 0;
    this->stateStartTime = millis();

    Serial.println("AnimatedSprite: Initialized, starting with mushroom sprite");
}

void AnimatedSprite::init() {
    Serial.println("AnimatedSprite: Initialized, starting animation cycle");
}

void AnimatedSprite::switchToNextSprite() {
    switch (currentSpriteType) {
        case MUSHROOM_SPRITE:
            currentSpriteType = HEDGEHOG_SPRITE_1;
            currentSprite = ciwei1;
            break;
        case HEDGEHOG_SPRITE_1:
            currentSpriteType = HEDGEHOG_SPRITE_2;
            currentSprite = ciwei2;
            break;
        case HEDGEHOG_SPRITE_2:
            currentSpriteType = HEDGEHOG_SPRITE_1;
            currentSprite = ciwei1;
            break;
    }
}

void AnimatedSprite::updateSpriteAnimation() {
    unsigned long currentTime = millis();

    // 只有刺猬精灵需要动画切换
    if ((currentSpriteType == HEDGEHOG_SPRITE_1 || currentSpriteType == HEDGEHOG_SPRITE_2) &&
        (movementState == MOVING_RIGHT || movementState == ENTERING_SCREEN) &&
        currentTime - lastSpriteChange >= SPRITE_ANIMATION_INTERVAL) {

        switchToNextSprite();
        lastSpriteChange = currentTime;
    }
}

void AnimatedSprite::update() {
    unsigned long currentTime = millis();

    // 更新精灵动画
    updateSpriteAnimation();

    // 移动逻辑更新
    if (currentTime - lastMovementUpdate >= MOVEMENT_UPDATE_INTERVAL) {
        // 保存旧位置
        int16_t oldX = x;
        int16_t oldY = y;
        bool needsRedraw = false;

        switch (movementState) {
            case ENTERING_SCREEN:
                // 从屏幕右侧逐步移动到起始位置
                if (x > startX) {
                    x -= MOVEMENT_PACE;
                    needsRedraw = true;
                } else {
                    // 到达起始位置，开始停留
                    x = startX;
                    movementState = IDLE_AT_START;
                    stateStartTime = currentTime;
                }
                break;

            case IDLE_AT_START:
                if (currentTime - stateStartTime >= IDLE_DURATION) {
                    movementState = MOVING_RIGHT;
                }
                break;

            case MOVING_RIGHT:
                if (x < targetX) {
                    x += MOVEMENT_PACE;
                    needsRedraw = true;
                } else {
                    movementState = EXITING_SCREEN;
                }
                break;

            case EXITING_SCREEN:
                // 继续向右移动直到完全退出屏幕
                if (x < offScreenX) {
                    x += MOVEMENT_PACE;
                    needsRedraw = true;
                } else {
                    movementState = WAITING_OFF_SCREEN;
                    stateStartTime = currentTime;
                    needsRedraw = false; // 不需要重绘，因为已经在屏幕外
                }
                break;

            case WAITING_OFF_SCREEN:
                if (currentTime - stateStartTime >= OFF_SCREEN_WAIT_DURATION) {
                    // 切换到下一个精灵类型
                    if (currentSpriteType == MUSHROOM_SPRITE) {
                        currentSpriteType = HEDGEHOG_SPRITE_1;
                        currentSprite = ciwei1;
                    } else {
                        currentSpriteType = MUSHROOM_SPRITE;
                        currentSprite = mogu;
                    }

                    // 重置位置并开始进入屏幕
                    x = offScreenX;
                    movementState = ENTERING_SCREEN;
                    lastSpriteChange = currentTime; // 重置动画计时器
                }
                break;
        }

        // 如果位置发生变化，重绘
        if (needsRedraw) {
            // 只有在屏幕内时才进行重绘操作
            if (oldX < 192 || x < 192) {
                // 重绘背景区域（只重绘屏幕内的部分）
                int16_t redrawX = (oldX < 0) ? 0 : oldX;
                int16_t redrawWidth = getWidth();
                if (redrawX + redrawWidth > 192) {
                    redrawWidth = 192 - redrawX;
                }
                if (redrawWidth > 0) {
                    redrawBackground(redrawX, oldY, redrawWidth, getHeight());
                }
            }

            // 只有在屏幕内时才绘制精灵
            if (x < 192) {
                drawScaledSprite(x, y, currentSprite);
            }
        }

        lastMovementUpdate = currentTime;
    }
}

void AnimatedSprite::draw() {
    // 只有在屏幕内时才绘制精灵
    if (x < 192) {
        drawScaledSprite(x, y, currentSprite);
    }
}

void AnimatedSprite::drawScaledSprite(int16_t x, int16_t y, const uint16_t* sprite) {
    // 使用带图层检测的透明位图绘制函数，3倍缩放，精灵层优先级
    drawTransparentBitmapWithLayerCheck(x, y, sprite, 16, 16, 3, LAYER_SPRITES);
}

void AnimatedSprite::redrawBackground(int16_t x, int16_t y, int16_t w, int16_t h) {
    // 先用天空色填充整个区域
    if (useFrameBuffer) {
        updateRectVirtual(x, y, w, h, SKY_COLOR);
    } else {
        fillRectVirtual(x, y, w, h, SKY_COLOR);
    }

    // 重绘山脉部分（山脉位置：X=0, Y=102, 宽度=60, 高度=66）
    int16_t hillX = 0, hillY = 102, hillW = 60, hillH = 66;
    if (x < hillX + hillW && x + w > hillX && y < hillY + hillH && y + h > hillY) {
        // 计算重叠区域
        int16_t overlapX = (x > hillX) ? x : hillX;
        int16_t overlapY = (y > hillY) ? y : hillY;
        int16_t overlapW = ((x + w < hillX + hillW) ? (x + w) : (hillX + hillW)) - overlapX;
        int16_t overlapH = ((y + h < hillY + hillH) ? (y + h) : (hillY + hillH)) - overlapY;

        // 重绘山脉的重叠部分（3倍缩放）
        for (int j = 0; j < overlapH; j++) {
            for (int i = 0; i < overlapW; i++) {
                int16_t hillPixelX = (overlapX + i - hillX) / 3;
                int16_t hillPixelY = (overlapY + j - hillY) / 3;
                if (hillPixelX >= 0 && hillPixelX < 20 && hillPixelY >= 0 && hillPixelY < 22) {
                    uint16_t pixel = HILL[hillPixelY * 20 + hillPixelX];
                    if (useFrameBuffer) {
                        updatePixelVirtual(overlapX + i, overlapY + j, pixel);
                    } else {
                        drawPixelVirtual(overlapX + i, overlapY + j, pixel);
                    }
                }
            }
        }
    }

    // 重绘树丛部分（树丛位置：X=129, Y=141, 宽度=63, 高度=27）
    int16_t bushX = 129, bushY = 141, bushW = 63, bushH = 27;
    if (x < bushX + bushW && x + w > bushX && y < bushY + bushH && y + h > bushY) {
        // 计算重叠区域
        int16_t overlapX = (x > bushX) ? x : bushX;
        int16_t overlapY = (y > bushY) ? y : bushY;
        int16_t overlapW = ((x + w < bushX + bushW) ? (x + w) : (bushX + bushW)) - overlapX;
        int16_t overlapH = ((y + h < bushY + bushH) ? (y + h) : (bushY + bushH)) - overlapY;

        // 重绘树丛的重叠部分（3倍缩放）
        for (int j = 0; j < overlapH; j++) {
            for (int i = 0; i < overlapW; i++) {
                int16_t bushPixelX = (overlapX + i - bushX) / 3;
                int16_t bushPixelY = (overlapY + j - bushY) / 3;
                if (bushPixelX >= 0 && bushPixelX < 21 && bushPixelY >= 0 && bushPixelY < 9) {
                    uint16_t pixel = BUSH[bushPixelY * 21 + bushPixelX];
                    if (pixel != _MASK) {  // 树丛也有透明像素
                        if (useFrameBuffer) {
                            updatePixelVirtual(overlapX + i, overlapY + j, pixel);
                        } else {
                            drawPixelVirtual(overlapX + i, overlapY + j, pixel);
                        }
                    }
                }
            }
        }
    }

    // 重绘地面部分（地面位置：Y=168, 高度=24）
    int16_t groundY = 168, groundH = 24;
    if (y < groundY + groundH && y + h > groundY) {
        // 计算重叠区域
        int16_t overlapY = (y > groundY) ? y : groundY;
        int16_t overlapH = ((y + h < groundY + groundH) ? (y + h) : (groundY + groundH)) - overlapY;

        // 重绘地面的重叠部分（使用3倍缩放的地面瓦片）
        for (int j = 0; j < overlapH; j++) {
            for (int i = 0; i < w; i++) {
                // 地面瓦片是8x8重复的，缩放后是24x24
                int16_t groundPixelX = ((x + i) / 3) % 8;
                int16_t groundPixelY = ((overlapY + j - groundY) / 3) % 8;

                if (groundPixelY >= 0 && groundPixelY < 8 && groundPixelX >= 0 && groundPixelX < 8) {
                    uint16_t pixel = GROUND_TILE[groundPixelY * 8 + groundPixelX];
                    if (useFrameBuffer) {
                        updatePixelVirtual(x + i, overlapY + j, pixel);
                    } else {
                        drawPixelVirtual(x + i, overlapY + j, pixel);
                    }
                }
            }
        }
    }

    // 重绘云朵动画
    extern Scene scene;
    scene.redrawCloudsInRegion(x, y, w, h);
}

// ==================== BOSS1精灵类实现 ====================

Boss1::Boss1(int16_t x, int16_t y) {
    this->x = x;
    this->y = y;
    this->currentFrame = FRAME_1;
    this->currentSprite = BOSS1_SPRITE;  // 初始使用第一帧
    this->lastFrameChange = 0;
}

void Boss1::init() {
    // 初始化时间
    unsigned long currentTime = millis();
    lastFrameChange = currentTime;

    // 绘制初始状态的BOSS1精灵
    drawSprite(x, y);
}

void Boss1::update() {
    unsigned long currentTime = millis();

    // 处理动画帧切换
    if (currentTime - lastFrameChange >= FRAME_ANIMATION_INTERVAL) {
        // 保存旧位置用于重绘背景
        int16_t oldX = x;
        int16_t oldY = y;

        // 重绘背景区域（清除旧的精灵）
        if (useFrameBuffer) {
            updateRectVirtual(oldX, oldY, getWidth(), getHeight(), SKY_COLOR);
        } else {
            fillRectVirtual(oldX, oldY, getWidth(), getHeight(), SKY_COLOR);
        }

        // 重绘背景元素（山脉、地面等）
        redrawBackground(oldX, oldY, getWidth(), getHeight());

        // 切换到下一帧
        switchToNextFrame();

        // 绘制新帧
        drawSprite(x, y);

        lastFrameChange = currentTime;
    }
}

void Boss1::switchToNextFrame() {
    // 循环切换动画帧：1 -> 2 -> 3 -> 4 -> 1 -> ...
    switch (currentFrame) {
        case FRAME_1:
            currentFrame = FRAME_2;
            currentSprite = BOSS2_SPRITE;
            break;
        case FRAME_2:
            currentFrame = FRAME_3;
            currentSprite = BOSS3_SPRITE;
            break;
        case FRAME_3:
            currentFrame = FRAME_4;
            currentSprite = BOSS4_SPRITE;
            break;
        case FRAME_4:
            currentFrame = FRAME_1;
            currentSprite = BOSS1_SPRITE;
            break;
    }
}

void Boss1::draw() {
    // 绘制当前帧的BOSS精灵
    drawSprite(x, y);
}

void Boss1::drawSprite(int16_t x, int16_t y) {
    // 绘制32x32像素的BOSS精灵，2倍缩放
    for (int j = 0; j < BOSS1_SIZE[1]; j++) {
        for (int i = 0; i < BOSS1_SIZE[0]; i++) {
            uint16_t color = currentSprite[j * BOSS1_SIZE[0] + i];

            // 跳过透明像素（使用0x949f作为透明色）
            if (color == 0x949f) continue;

            // 2倍缩放：每个原始像素绘制成2x2的像素块
            for (int sy = 0; sy < 2; sy++) {
                for (int sx = 0; sx < 2; sx++) {
                    int16_t pixelX = x + (i * 2) + sx;
                    int16_t pixelY = y + (j * 2) + sy;

                    // 绘制像素到虚拟画布
                    if (useFrameBuffer) {
                        updatePixelVirtual(pixelX, pixelY, color);
                    } else {
                        drawPixelVirtual(pixelX, pixelY, color);
                    }
                }
            }
        }
    }
}

void Boss1::redrawBackground(int16_t x, int16_t y, int16_t w, int16_t h) {
    // 先用天空色填充整个区域
    if (useFrameBuffer) {
        updateRectVirtual(x, y, w, h, SKY_COLOR);
    } else {
        fillRectVirtual(x, y, w, h, SKY_COLOR);
    }

    // 重绘山脉部分（山脉位置：X=0, Y=102, 宽度=60, 高度=66）
    int16_t hillX = 0, hillY = 102, hillW = 60, hillH = 66;
    if (x < hillX + hillW && x + w > hillX && y < hillY + hillH && y + h > hillY) {
        // 计算重叠区域
        int16_t overlapX = (x > hillX) ? x : hillX;
        int16_t overlapY = (y > hillY) ? y : hillY;
        int16_t overlapW = ((x + w < hillX + hillW) ? (x + w) : (hillX + hillW)) - overlapX;
        int16_t overlapH = ((y + h < hillY + hillH) ? (y + h) : (hillY + hillH)) - overlapY;

        // 重绘山脉的重叠部分（使用3倍缩放的山脉瓦片）
        for (int j = 0; j < overlapH; j++) {
            for (int i = 0; i < overlapW; i++) {
                // 山脉瓦片是20x22重复的，缩放后是60x66
                int16_t hillPixelX = ((overlapX + i - hillX) / 3) % 20;
                int16_t hillPixelY = ((overlapY + j - hillY) / 3) % 22;

                if (hillPixelY >= 0 && hillPixelY < 22 && hillPixelX >= 0 && hillPixelX < 20) {
                    uint16_t pixel = HILL[hillPixelY * 20 + hillPixelX];
                    if (useFrameBuffer) {
                        updatePixelVirtual(overlapX + i, overlapY + j, pixel);
                    } else {
                        drawPixelVirtual(overlapX + i, overlapY + j, pixel);
                    }
                }
            }
        }
    }

    // 重绘地面部分（地面位置：Y=168, 高度=24）
    int16_t groundY = VIRTUAL_HEIGHT - GROUND_HEIGHT;
    int16_t groundH = GROUND_HEIGHT;
    if (y < groundY + groundH && y + h > groundY) {
        // 计算重叠区域
        int16_t overlapY = (y > groundY) ? y : groundY;
        int16_t overlapH = ((y + h < groundY + groundH) ? (y + h) : (groundY + groundH)) - overlapY;

        // 重绘地面的重叠部分（使用3倍缩放的地面瓦片）
        for (int j = 0; j < overlapH; j++) {
            for (int i = 0; i < w; i++) {
                // 地面瓦片是8x8重复的，缩放后是24x24
                int16_t groundPixelX = ((x + i) / 3) % 8;
                int16_t groundPixelY = ((overlapY + j - groundY) / 3) % 8;

                if (groundPixelY >= 0 && groundPixelY < 8 && groundPixelX >= 0 && groundPixelX < 8) {
                    uint16_t pixel = GROUND_TILE[groundPixelY * 8 + groundPixelX];
                    if (useFrameBuffer) {
                        updatePixelVirtual(x + i, overlapY + j, pixel);
                    } else {
                        drawPixelVirtual(x + i, overlapY + j, pixel);
                    }
                }
            }
        }
    }

    // 重绘云朵动画
    extern Scene scene;
    scene.redrawCloudsInRegion(x, y, w, h);
}